#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PIL import Image, ImageDraw
import os

def create_simple_piece(text, size=(73, 73), is_red=False):
    """创建一个简单的棋子图像"""
    # 创建新图像
    bg_color = (180, 0, 0) if is_red else (0, 0, 0)  # 红色或黑色背景
    img = Image.new('RGB', size, bg_color)
    draw = ImageDraw.Draw(img)
    
    # 绘制边框
    draw.ellipse([(2, 2), (size[0]-3, size[1]-3)], outline=(255, 255, 255), width=2)
    
    # 中心位置放一个字符
    # 由于字体加载可能有问题，我们直接绘制一个代表性的形状
    center_x, center_y = size[0] // 2, size[1] // 2
    radius = min(size[0], size[1]) // 4
    
    # 为不同的棋子绘制不同的标识符
    if text in ["帅", "将"]:  # 将/帅
        draw.rectangle([(center_x-radius, center_y-radius), 
                        (center_x+radius, center_y+radius)], 
                       outline=(255, 255, 255))
    elif text in ["士", "仕"]:  # 士/仕
        draw.line([(center_x-radius, center_y-radius), 
                   (center_x+radius, center_y+radius)], 
                   fill=(255, 255, 255), width=2)
        draw.line([(center_x-radius, center_y+radius), 
                   (center_x+radius, center_y-radius)], 
                   fill=(255, 255, 255), width=2)
    elif text in ["象", "相"]:  # 象/相
        draw.ellipse([(center_x-radius, center_y-radius), 
                      (center_x+radius, center_y+radius)], 
                      outline=(255, 255, 255), width=2)
    elif text in ["马"]:  # 马
        points = [
            (center_x, center_y-radius),
            (center_x+radius, center_y),
            (center_x, center_y+radius),
            (center_x-radius, center_y)
        ]
        draw.polygon(points, outline=(255, 255, 255))
    elif text in ["车"]:  # 车
        draw.rectangle([(center_x-radius, center_y-radius), 
                        (center_x+radius, center_y+radius)], 
                        outline=(255, 255, 255), width=2)
    elif text in ["炮"]:  # 炮
        draw.arc([(center_x-radius, center_y-radius), 
                  (center_x+radius, center_y+radius)], 
                  0, 360, fill=(255, 255, 255), width=2)
    elif text in ["兵", "卒"]:  # 兵/卒
        draw.ellipse([(center_x-radius//2, center_y-radius//2), 
                      (center_x+radius//2, center_y+radius//2)], 
                      fill=(255, 255, 255))
    
    return img

def generate_all_pieces():
    """生成所有棋子图片"""
    # 确保输出目录存在
    output_dir = "assets/images/pieces"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")
    
    # 定义棋子
    red_pieces = {
        "general": "帅",
        "advisor": "士", 
        "elephant": "相",
        "horse": "马",
        "chariot": "车",
        "cannon": "炮",
        "soldier": "兵"
    }
    
    black_pieces = {
        "general": "将",
        "advisor": "仕",
        "elephant": "象",
        "horse": "马",
        "chariot": "车", 
        "cannon": "炮",
        "soldier": "卒"
    }
    
    # 生成红方棋子
    for piece_type, text in red_pieces.items():
        filename = f"{output_dir}/red_{piece_type}.png"
        img = create_simple_piece(text, is_red=True)
        img.save(filename)
        print(f"已生成红方{text}棋子图片: {filename}")
    
    # 生成黑方棋子
    for piece_type, text in black_pieces.items():
        filename = f"{output_dir}/black_{piece_type}.png"
        img = create_simple_piece(text, is_red=False)
        img.save(filename)
        print(f"已生成黑方{text}棋子图片: {filename}")
    
    print("所有棋子图片生成完毕！")

if __name__ == "__main__":
    print("开始生成中国象棋棋子图片...")
    generate_all_pieces()
