import os
import sys
import subprocess

# 创建日志目录
if not os.path.exists('logs'):
    os.makedirs('logs')

# 运行主程序并捕获输出
try:
    # 使用子进程运行主程序
    process = subprocess.run(
        ['python', 'main.py'],
        capture_output=True,
        text=True,
        check=False
    )
    
    # 将输出写入日志文件
    with open('logs/output.log', 'w', encoding='utf-8') as f:
        f.write("=== 标准输出 ===\n")
        f.write(process.stdout)
        f.write("\n\n=== 错误输出 ===\n")
        f.write(process.stderr)
    
    print(f"程序已完成，退出码: {process.returncode}")
    print("日志已写入 logs/output.log")
    
    # 如果有错误输出，打印到控制台
    if process.stderr:
        print("\n错误信息:")
        print(process.stderr)
        
except Exception as e:
    # 如果运行脚本本身出错
    print(f"运行脚本时出错: {e}")
    
    # 将错误写入日志文件
    with open('logs/error.log', 'w', encoding='utf-8') as f:
        f.write(f"运行脚本时出错: {e}")
        import traceback
        traceback.print_exc(file=f)
