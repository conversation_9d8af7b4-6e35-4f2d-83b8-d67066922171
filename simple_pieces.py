import pygame
import os

# 初始化pygame
pygame.init()

# 设置棋子大小
PIECE_SIZE = (73, 73)

# 确保输出目录存在
output_dir = "assets/images/pieces"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

def draw_piece(color, symbol, filename):
    """绘制一个棋子并保存为PNG文件"""
    # 创建一个透明的Surface
    surface = pygame.Surface(PIECE_SIZE, pygame.SRCALPHA)
    
    # 设置背景颜色
    bg_color = (180, 0, 0) if color == "red" else (0, 0, 0)
    
    # 绘制圆形背景
    center = (PIECE_SIZE[0] // 2, PIECE_SIZE[1] // 2)
    radius = PIECE_SIZE[0] // 2 - 2
    pygame.draw.circle(surface, bg_color, center, radius)
    
    # 绘制白色边框
    pygame.draw.circle(surface, (255, 255, 255), center, radius, 2)
    
    # 尝试绘制文字
    try:
        # 尝试加载字体（Windows系统）
        font_paths = [
            "C:/Windows/Fonts/simhei.ttf",
            "C:/Windows/Fonts/simsun.ttc",
            "C:/Windows/Fonts/msyh.ttc",
            "C:/Windows/Fonts/STKAITI.ttf"
        ]
        
        # 尝试每个字体，直到找到一个可用的
        font = None
        for path in font_paths:
            if os.path.exists(path):
                try:
                    font = pygame.font.Font(path, 36)
                    break
                except:
                    continue
        
        # 如果找到字体，绘制文字
        if font:
            text = font.render(symbol, True, (255, 255, 255))
            text_rect = text.get_rect(center=center)
            surface.blit(text, text_rect)
        else:
            print(f"警告：未能找到合适的字体来渲染'{symbol}'")
            
            # 如果找不到字体，绘制简单的标识符
            # 根据棋子类型绘制不同形状
            small_radius = radius // 2
            if symbol in ["帅", "将"]:
                pygame.draw.rect(surface, (255, 255, 255), 
                                (center[0]-small_radius, center[1]-small_radius, 
                                small_radius*2, small_radius*2), 2)
            elif symbol in ["士", "仕"]:
                pygame.draw.line(surface, (255, 255, 255), 
                                (center[0]-small_radius, center[1]-small_radius), 
                                (center[0]+small_radius, center[1]+small_radius), 2)
                pygame.draw.line(surface, (255, 255, 255), 
                                (center[0]-small_radius, center[1]+small_radius), 
                                (center[0]+small_radius, center[1]-small_radius), 2)
            elif symbol in ["象", "相"]:
                pygame.draw.circle(surface, (255, 255, 255), center, small_radius, 2)
            elif symbol in ["马"]:
                points = [
                    (center[0], center[1]-small_radius),
                    (center[0]+small_radius, center[1]),
                    (center[0], center[1]+small_radius),
                    (center[0]-small_radius, center[1])
                ]
                pygame.draw.polygon(surface, (255, 255, 255), points, 2)
            else:  # 其他棋子画一个点
                pygame.draw.circle(surface, (255, 255, 255), center, small_radius//2, 0)
                
    except Exception as e:
        print(f"绘制'{symbol}'时出错: {e}")
    
    # 保存图片
    try:
        full_path = os.path.join(output_dir, filename)
        pygame.image.save(surface, full_path)
        print(f"已保存: {full_path}")
        return True
    except Exception as e:
        print(f"保存'{filename}'失败: {e}")
        return False

# 生成所有棋子
def generate_all_pieces():
    print("开始生成中国象棋棋子图片...")
    
    # 定义红方棋子
    red_pieces = {
        "red_general.png": "帅",
        "red_advisor.png": "士",
        "red_elephant.png": "相",
        "red_horse.png": "马",
        "red_chariot.png": "车",
        "red_cannon.png": "炮",
        "red_soldier.png": "兵"
    }
    
    # 定义黑方棋子
    black_pieces = {
        "black_general.png": "将",
        "black_advisor.png": "仕",
        "black_elephant.png": "象",
        "black_horse.png": "马",
        "black_chariot.png": "车",
        "black_cannon.png": "炮",
        "black_soldier.png": "卒"
    }
    
    # 生成红方棋子
    red_success = 0
    for filename, symbol in red_pieces.items():
        if draw_piece("red", symbol, filename):
            red_success += 1
            
    # 生成黑方棋子
    black_success = 0
    for filename, symbol in black_pieces.items():
        if draw_piece("black", symbol, filename):
            black_success += 1
    
    print(f"共成功生成 {red_success + black_success} 个棋子图片")
    print(f"红方: {red_success}/{len(red_pieces)}, 黑方: {black_success}/{len(black_pieces)}")

# 执行生成
if __name__ == "__main__":
    generate_all_pieces()
    pygame.quit()
