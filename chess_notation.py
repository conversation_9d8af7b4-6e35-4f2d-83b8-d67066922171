import os
import json

class ChessNotation:
    """中国象棋传统棋谱管理类"""
    
    # 横向坐标映射（0-8映射到a-i）
    FILE_MAP = {0: 'a', 1: 'b', 2: 'c', 3: 'd', 4: 'e', 5: 'f', 6: 'g', 7: 'h', 8: 'i'}
    FILE_MAP_REVERSE = {'a': 0, 'b': 1, 'c': 2, 'd': 3, 'e': 4, 'f': 5, 'g': 6, 'h': 7, 'i': 8}
    
    # 棋子中文名称映射
    PIECE_NAMES = {
        0: {  # 红方
            "general": "帅",
            "advisor": "仕",
            "elephant": "相",
            "horse": "马",
            "chariot": "车",
            "cannon": "炮",
            "soldier": "兵"
        },
        1: {  # 黑方
            "general": "将",
            "advisor": "士",
            "elephant": "象",
            "horse": "马",
            "chariot": "车",
            "cannon": "炮",
            "soldier": "卒"
        }
    }
    
    def __init__(self):
        """初始化棋谱管理器"""
        self.moves = []  # 记录所有着法
        self.current_move_index = -1  # 当前回放到的着法索引
    
    def pos_to_notation(self, x, y):
        """将坐标转换为传统记谱法"""
        # 横坐标a-i对应0-8
        file = self.FILE_MAP[x]
        # 纵坐标为0-9
        rank = str(y)
        return f"{file}{rank}"
    
    def notation_to_pos(self, notation):
        """将传统记谱法转换为坐标"""
        if len(notation) < 2:
            return None
        
        file = notation[0].lower()
        rank = notation[1:]
        
        # 检查有效性
        if file not in self.FILE_MAP_REVERSE or not rank.isdigit():
            return None
        
        x = self.FILE_MAP_REVERSE[file]
        y = int(rank)
        
        # 验证坐标范围
        if x < 0 or x > 8 or y < 0 or y > 9:
            return None
            
        return (x, y)
    
    def record_move(self, piece, from_pos, to_pos):
        """记录一步棋"""
        from_x, from_y = from_pos
        to_x, to_y = to_pos
        
        # 获取棋子中文名称
        piece_name = "未知"
        if hasattr(piece, 'piece_type') and hasattr(piece, 'player'):
            piece_name = self.PIECE_NAMES.get(piece.player, {}).get(piece.piece_type, "未知")
        
        # 转换为传统记谱法
        from_notation = self.pos_to_notation(from_x, from_y)
        to_notation = self.pos_to_notation(to_x, to_y)
        
        # 创建着法记录
        move_notation = f"{piece_name} {from_notation}-{to_notation}"
        
        # 添加到着法列表
        self.moves.append({
            "piece": piece.piece_type if hasattr(piece, 'piece_type') else "unknown",
            "player": piece.player if hasattr(piece, 'player') else 0,
            "from": from_pos,
            "to": to_pos,
            "notation": move_notation
        })
        
        # 更新当前回放索引
        self.current_move_index = len(self.moves) - 1
        
        # 返回着法记录
        return move_notation
    
    def get_move_history(self):
        """获取完整的着法历史"""
        return self.moves
    
    def get_pgn_string(self):
        """获取PGN格式的棋谱字符串"""
        pgn = ""
        for i, move in enumerate(self.moves):
            move_number = i // 2 + 1
            if i % 2 == 0:  # 红方
                pgn += f"{move_number}. {move['notation']} "
            else:  # 黑方
                pgn += f"{move['notation']} "
        return pgn.strip()
    
    def save_pgn(self, filename):
        """保存棋谱到文件"""
        pgn_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "pgn", filename)
        
        # 确保pgn目录存在
        os.makedirs(os.path.dirname(pgn_path), exist_ok=True)
        
        # 构建棋谱数据
        pgn_data = {
            "moves": self.moves,
            "pgn_string": self.get_pgn_string()
        }
        
        # 保存到文件
        try:
            with open(pgn_path, 'w', encoding='utf-8') as f:
                json.dump(pgn_data, f, ensure_ascii=False, indent=2)
            return True, f"棋谱已保存到 {pgn_path}"
        except (IOError, PermissionError, FileNotFoundError) as e:
            return False, f"保存棋谱失败: {e}"
    
    def load_pgn(self, filename):
        """从文件加载棋谱"""
        pgn_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "pgn", filename)
        
        # 检查文件是否存在
        if not os.path.exists(pgn_path):
            return False, f"棋谱文件不存在: {pgn_path}"
        
        # 加载棋谱
        try:
            with open(pgn_path, 'r', encoding='utf-8') as f:
                pgn_data = json.load(f)
            
            self.moves = pgn_data.get("moves", [])
            self.current_move_index = -1
            
            return True, f"成功加载棋谱: {pgn_path}"
        except (IOError, json.JSONDecodeError, FileNotFoundError) as e:
            return False, f"加载棋谱失败: {e}"
    
    def print_move(self, move_index=None):
        """打印指定索引的着法，默认为最后一步"""
        if not self.moves:
            return "没有着法记录"
            
        if move_index is None:
            move_index = self.current_move_index
            
        if move_index < 0 or move_index >= len(self.moves):
            return "无效的着法索引"
        
        move = self.moves[move_index]
        player_name = "红方" if move.get("player", 0) == 0 else "黑方"
        move_number = move_index // 2 + 1
        half_move = "前" if move_index % 2 == 0 else "后"
        
        return f"第{move_number}回合{half_move}: {player_name} {move.get('notation', '未知')}"
