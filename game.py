import pygame
from board import Board
from menu import Menu
from constants import STATE_MENU, STATE_PLAYING, STATE_GAME_OVER, RED_PLAYER, BLACK_PLAYER, \
    TOOLBAR_HEIGHT, WINDOW_WIDTH, WINDOW_HEIGHT, BOARD_OFFSET_X, BOARD_OFFSET_Y, \
    SOUND_MOVE, SOUND_CAPTURE, SOUND_CHECK, SOUND_START
from toolbar import Toolbar
from sound_manager import SoundManager
from opening_book import OpeningBook
from chess_notation import ChessNotation
import threading
import time
import queue
import copy
import os


class Game:
    """游戏主类，管理游戏状态和界面"""

    def __init__(self, screen):
        """初始化游戏"""
        print("初始化游戏类...")
        self.screen = screen
        self.clock = pygame.time.Clock()
        self.target_fps = 60
        self.board = Board()
        self.menu = Menu(self)
        self.state = STATE_MENU
        self.current_player = RED_PLAYER
        self.selected_piece = None
        self.valid_moves = []
        self.game_over = False
        self.winner = None

        # 动画相关属性
        self.animating = False
        self.animation_start_time = 0
        self.animation_duration = 0.3  # 动画持续时间（秒）
        self.animation_piece = None
        self.animation_start_pos = None
        self.animation_end_pos = None
        self.last_render_time = time.time()
        self.render_interval = 1/60  # 60 FPS

        # AI相关属性
        self.vs_ai = False
        self.ai_difficulty = 1
        self.ai_engine = None
        self.ai_thread = None
        self.ai_thinking = False
        self.exit_flag = False
        self.ai_move_queue = queue.Queue()
        self.player_side = BLACK_PLAYER  # 默认玩家执黑（底部）
        self.board_lock = threading.RLock()  # 添加线程锁

        # 历史着法相关
        self.move_history = []
        self.captured_pieces_history = []
        self.replay_mode = False
        self.history_index = -1

        # 棋谱相关
        self.notation = ChessNotation()
        self.recommended_moves = []

        # 工具栏

        # 创建音效管理器
        self.sound_manager = SoundManager()

        # 加载开局库
        self.opening_book = OpeningBook()

        # 棋盘翻转标志
        self.board_flipped = False

        # 状态栏属性
        self.status_bar_height = 50
        self.status_bar_color = (240, 240, 240)
        self.status_bar_border_color = (180, 180, 180)
        self.status_bar_text_color = (0, 0, 0)
        self.status_bar_font = None
        self.status_text = ""
        self.status_timeout = 0
        self.ai_start_time = None
        self.evaluation_result = None

        # 玩家思考时间限制（单位：秒）
        self.move_time_limit = 5  # 默认5秒超时
        self.move_start_time = None  # 开始思考的时间
        self.show_time_warning = False  # 是否显示时间警告

        # 调整棋盘位置
        self.adjust_board_position()

        # 工具栏必须在所有相关方法定义之后初始化
        self.toolbar = Toolbar(self)
        self.toolbar.create_buttons() # 在Game对象完全初始化后创建按钮

        print("游戏类初始化完成")

    def reset_game(self):
        """重置游戏状态"""
        self.state = STATE_PLAYING
        self.board = Board()  # 重新初始化棋盘
        self.current_player = RED_PLAYER
        self.selected_piece = None
        self.game_over = False
        self.animating = False
        self.animation_queue = []
        self.recommended_moves = []
        self.move_history = []
        self.captured_pieces_history = []
        self.replay_mode = False
        self.history_index = -1
        self.notation = ChessNotation()

        # 重置AI状态
        if hasattr(self, 'ai_thinking'):
            self.ai_thinking = False
        if hasattr(self, 'ai_thread') and self.ai_thread and self.ai_thread.is_alive():
            self.exit_flag = True
            self.ai_thread.join(timeout=0.1)

        # 播放游戏开始音效
        self.play_sound("start")

    def start_game(self, vs_ai=False):
        """开始新游戏"""
        self.reset_game()
        self.vs_ai = vs_ai

        # 初始化AI引擎
        if vs_ai:
            from ai.engine import AIEngine
            self.ai_engine = AIEngine(difficulty=self.ai_difficulty)

    def save_pgn_dialog(self):
        """保存棋局到PGN文件"""
        import tkinter as tk
        from tkinter import filedialog
        root = tk.Tk()
        root.withdraw()
        file_path = filedialog.asksaveasfilename(
            defaultextension=".pgn",
            filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")]
        )
        if file_path:
            self.notation.save_to_file(file_path)

    def open_pgn_dialog(self):
        """打开PGN棋局文件"""
        import tkinter as tk
        from tkinter import filedialog
        root = tk.Tk()
        root.withdraw()
        file_path = filedialog.askopenfilename(
            filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")]
        )
        if file_path:
            try:
                # 重置游戏状态
                self.reset_game()

                # 加载PGN文件
                self.notation.load_from_file(file_path)

                # 从棋谱重建棋局
                self.reconstruct_from_notation()

                # 提示加载成功
                self.status_text = f"已加载棋局: {os.path.basename(file_path)}"
                self.status_timeout = time.time() + 3  # 显示3秒
            except Exception as e:
                self.status_text = f"加载棋局失败: {str(e)}"
                self.status_timeout = time.time() + 3  # 显示3秒
                print(f"加载PGN文件出错: {e}")

    def reconstruct_from_notation(self):
        """从棋谱重建棋局"""
        # 清空当前棋盘
        self.board = Board()

        # 获取棋谱中的所有着法
        moves = self.notation.get_moves()

        # 按顺序执行每个着法
        for move in moves:
            from_pos = (move['from_x'], move['from_y'])
            to_pos = (move['to_x'], move['to_y'])
            piece = self.board.get_piece(*from_pos)
            if piece:
                self.board.move_piece(from_pos, *to_pos)

        # 设置当前玩家
        self.current_player = RED_PLAYER if len(
            moves) % 2 == 0 else BLACK_PLAYER

    def export_board_image(self):
        """导出当前棋盘为图片"""
        import tkinter as tk
        from tkinter import filedialog
        import pygame

        # 创建临时surface渲染棋盘
        board_surface = pygame.Surface((self.board.width, self.board.height))
        self.board.render(board_surface, None)  # 不渲染选中状态

        # 弹出保存文件对话框
        root = tk.Tk()
        root.withdraw()
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"),
                       ("JPEG files", "*.jpg"), ("All files", "*.*")]
        )

        if file_path:
            try:
                # 保存图片
                pygame.image.save(board_surface, file_path)
                self.status_text = f"棋盘已保存为: {os.path.basename(file_path)}"
                self.status_timeout = time.time() + 3  # 显示3秒
            except Exception as e:
                self.status_text = f"保存图片失败: {str(e)}"
                self.status_timeout = time.time() + 3  # 显示3秒

    def flip_board(self):
        """翻转棋盘视图"""
        # 切换棋盘翻转状态
        self.board_flipped = not self.board_flipped
        self.board.flipped = self.board_flipped

        # 更新状态提示
        if self.board_flipped:
            self.status_text = "棋盘已翻转"
        else:
            self.status_text = "棋盘已恢复原始方向"
        self.status_timeout = time.time() + 2  # 显示2秒

    def view_text_notation(self):
        """查看文本棋谱"""
        import tkinter as tk
        from tkinter import scrolledtext

        # 创建窗口
        window = tk.Tk()
        window.title("棋谱记录")
        window.geometry("600x400")

        # 创建文本框
        text_area = scrolledtext.ScrolledText(
            window, wrap=tk.WORD, width=70, height=20)
        text_area.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

        # 获取棋谱文本
        notation_text = self.notation.get_text_notation()

        # 插入文本
        text_area.insert(tk.INSERT, notation_text)
        text_area.configure(state='disabled')  # 设为只读

        # 添加关闭按钮
        close_button = tk.Button(window, text="关闭", command=window.destroy)
        close_button.pack(pady=5)

        # 运行窗口
        window.mainloop()

    def move_to_start(self):
        """回到起始局面"""
        if not self.move_history:  # 如果没有历史记录，直接返回
            return

        # 进入回放模式
        self.replay_mode = True

        # 重置棋盘到初始状态
        self.board = Board()
        self.current_player = RED_PLAYER
        self.selected_piece = None

        # 重置历史索引
        self.history_index = -1

        # 更新状态提示
        self.status_text = "已回到起始局面"
        self.status_timeout = time.time() + 2  # 显示2秒

        # 播放音效
        self.play_sound("move")

    def move_history_backward(self):
        """查看上一步棋"""
        if not self.move_history:  # 如果没有历史记录，直接返回
            return

        # 如果当前不是在回放模式，或者已经在最开始的位置
        if not self.replay_mode:
            # 进入回放模式，并设置历史索引为最后一步
            self.replay_mode = True
            self.history_index = len(self.move_history) - 1

            # 重建到倒数第二步的局面
            self.board = Board()
            self.current_player = RED_PLAYER

            # 重放到倒数第二步
            for i in range(self.history_index):
                move = self.move_history[i]
                from_pos = move['from']
                to_pos = move['to']
                self.board.move_piece(
                    from_pos, to_pos[0], to_pos[1], self.current_player)
                self.current_player = 1 - self.current_player  # 切换玩家

            # 更新状态提示
            self.status_text = f"查看上一步: {self.history_index + 1}/{len(self.move_history)}"
            self.status_timeout = time.time() + 2  # 显示2秒
        elif self.history_index > 0:
            # 已经在回放模式，向后移动一步
            self.history_index -= 1

            # 重建到当前历史索引的局面
            self.board = Board()
            self.current_player = RED_PLAYER

            # 重放到当前历史索引
            for i in range(self.history_index + 1):
                move = self.move_history[i]
                from_pos = move['from']
                to_pos = move['to']
                self.board.move_piece(
                    from_pos, to_pos[0], to_pos[1], self.current_player)
                self.current_player = 1 - self.current_player  # 切换玩家

            # 更新状态提示
            self.status_text = f"查看上一步: {self.history_index + 1}/{len(self.move_history)}"
            self.status_timeout = time.time() + 2  # 显示2秒
        else:
            # 已经在最开始的位置
            self.status_text = "已经是第一步"
            self.status_timeout = time.time() + 2  # 显示2秒

        # 清除选中状态
        self.selected_piece = None

        # 播放音效
        self.play_sound("move")

    def move_history_forward(self):
        """查看下一步棋"""
        if not self.move_history or not self.replay_mode:  # 如果没有历史记录或不在回放模式，直接返回
            return

        # 检查是否已经到达最后一步
        if self.history_index >= len(self.move_history) - 1:
            # 已经是最后一步
            self.status_text = "已经是最后一步"
            self.status_timeout = time.time() + 2  # 显示2秒
            return

        # 向前移动一步
        self.history_index += 1

        # 执行这一步的移动
        move = self.move_history[self.history_index]
        from_pos = move['from']
        to_pos = move['to']

        # 执行移动
        self.board.move_piece(
            from_pos, to_pos[0], to_pos[1], self.current_player)
        self.current_player = 1 - self.current_player  # 切换玩家

        # 更新状态提示
        self.status_text = f"查看下一步: {self.history_index + 1}/{len(self.move_history)}"
        self.status_timeout = time.time() + 2  # 显示2秒

        # 清除选中状态
        self.selected_piece = None

        # 播放音效
        self.play_sound("move")

        # 如果已经到达最后一步，提示可以退出回放模式
        if self.history_index == len(self.move_history) - 1:
            self.status_text = "已到达最后一步，可以继续对局"
            self.status_timeout = time.time() + 3  # 显示3秒

    def move_to_end(self):
        """回到最新局面（退出回放模式）"""
        if not self.replay_mode and self.history_index == len(self.move_history) - 1:
            # 已经在最新局面，且不在回放模式，无需操作
            self.status_text = "已是最新局面"
            self.status_timeout = time.time() + 2
            return

        # 重置游戏到最新状态
        self.reset_game() # 重置会清除历史，需要重新加载
        
        # 重新执行所有历史着法以达到最新局面
        temp_history = list(self.move_history) # 复制一份历史记录，因为reset_game会清空
        self.move_history = [] # 清空，以便reset_game后重新填充
        self.captured_pieces_history = [] # 清空，以便reset_game后重新填充

        self.board = Board() # 确保棋盘是初始状态
        self.current_player = RED_PLAYER # 从红方开始

        for move_data in temp_history:
            from_pos = move_data['from']
            to_pos = move_data['to']
            captured_piece = move_data.get('captured_piece')
            
            piece = self.board.get_piece(*from_pos)
            if piece:
                # 模拟移动，不进行合法性检查，因为是回放历史
                self.board.move_piece(from_pos, to_pos[0], to_pos[1], self.current_player)
                self.move_history.append(move_data) # 重新添加回历史
                if captured_piece:
                    self.captured_pieces_history.append(captured_piece)
                self.current_player = 1 - self.current_player # 切换玩家

        self.replay_mode = False
        self.history_index = len(self.move_history) - 1 if self.move_history else -1

        # 更新状态提示
        self.status_text = "已回到最新局面"
        self.status_timeout = time.time() + 2  # 显示2秒

        # 播放音效
        self.play_sound("move")

    # ... (保留其他所有现有方法不变) ...
