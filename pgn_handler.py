# pgn_handler.py
import os
import datetime
import tkinter as tk
from tkinter import filedialog
from board import Board  # 导入Board类

class PGNHandler:
    """处理PGN棋谱的类"""
    
    def __init__(self):
        """初始化PGN处理器"""
        self.default_event = "Chinese Chess Game"
        self.default_site = "Local Game"
        self.default_date = datetime.datetime.now().strftime("%Y.%m.%d")
        self.default_round = "1"
        self.default_red_player = "Red Player"
        self.default_black_player = "Black Player"
        
    def create_pgn_header(self):
        """创建PGN文件头部"""
        header = (
            f'[Event "{self.default_event}"]\n'
            f'[Site "{self.default_site}"]\n'
            f'[Date "{self.default_date}"]\n'
            f'[Round "{self.default_round}"]\n'
            f'[Red "{self.default_red_player}"]\n'
            f'[Black "{self.default_black_player}"]\n'
            f'[Result "*"]\n\n'
        )
        return header
    
    def move_to_pgn_notation(self, move, board, player):
        """将移动转换为PGN记谱格式
        
        Args:
            move: 一个包含起始位置和目标位置的元组 ((from_x, from_y), (to_x, to_y))
            board: 棋盘对象
            player: 玩家（0为红方，1为黑方）
            
        Returns:
            代表该移动的PGN记谱
        """
        from_pos, to_pos = move
        from_x, from_y = from_pos
        to_x, to_y = to_pos
        
        # 获取棋子
        piece = board.get_piece(from_x, from_y)
        if not piece:
            return "无效移动"
            
        # 中文数字（红方用）
        chinese_digits = ["一", "二", "三", "四", "五", "六", "七", "八", "九"]
        # 全角数字（黑方用）
        fullwidth_digits = ["１", "２", "３", "４", "５", "６", "７", "８", "９"]
        
        # 棋子中文名称
        piece_names = {
            # 红方棋子
            ("red", "general"): "帅",
            ("red", "advisor"): "士",
            ("red", "elephant"): "相",
            ("red", "horse"): "马",
            ("red", "chariot"): "车",
            ("red", "cannon"): "炮",
            ("red", "soldier"): "兵",
            # 黑方棋子
            ("black", "general"): "将",
            ("black", "advisor"): "仕",
            ("black", "elephant"): "象",
            ("black", "horse"): "馬",
            ("black", "chariot"): "車",
            ("black", "cannon"): "砲",
            ("black", "soldier"): "卒",
        }
        
        # 获取棋子名称
        piece_color = "red" if piece.player == 0 else "black"
        piece_name = piece_names.get((piece_color, piece.piece_type), "?")
        
        # 计算列数（红方和黑方对应的编号）
        if player == 0:  # 红方
            # 红方视角从右到左 1-9
            col_from = 9 - from_x
            col_to = 9 - to_x
            digits = chinese_digits
        else:  # 黑方
            # 黑方视角从左到右 1-9
            col_from = from_x + 1
            col_to = to_x + 1
            digits = fullwidth_digits
        
        # 确定移动方向
        if from_y == to_y:  # 平移
            direction = "平"
            position = digits[col_to - 1]
        elif (player == 0 and to_y < from_y) or (player == 1 and to_y > from_y):  # 进/前进
            direction = "进"
            # 兵卒和将帅的特殊处理
            if piece.piece_type in ["soldier", "general"]:
                # 计算前进的格数
                steps = abs(to_y - from_y)
                position = digits[steps - 1]
            else:
                position = digits[col_to - 1]
        else:  # 退/后退
            direction = "退"
            # 兵卒和将帅的特殊处理
            if piece.piece_type in ["soldier", "general"]:
                # 计算后退的格数
                steps = abs(to_y - from_y)
                position = digits[steps - 1]
            else:
                position = digits[col_to - 1]
        
        # 识别同类棋子在同一列的情况
        pieces_in_same_col = []
        for y in range(10):
            p = board.get_piece(from_x, y)
            if p and p.piece_type == piece.piece_type and p.player == piece.player and y != from_y:
                pieces_in_same_col.append(y)
        
        # 如果有同类棋子在同一列，需要区分前后
        pos_indicator = ""
        if pieces_in_same_col:
            # 对于红方，y值大的为"前"，y值小的为"后"
            # 对于黑方，y值小的为"前"，y值大的为"后"
            if player == 0:  # 红方
                if all(y > from_y for y in pieces_in_same_col):
                    pos_indicator = "后"
                else:
                    pos_indicator = "前"
            else:  # 黑方
                if all(y < from_y for y in pieces_in_same_col):
                    pos_indicator = "后"
                else:
                    pos_indicator = "前"
        else:
            # 没有同类棋子，使用列数
            pos_indicator = digits[col_from - 1]
        
        # 组合成传统记谱
        notation = f"{piece_name}{pos_indicator}{direction}{position}"
        return notation
    
    def moves_to_pgn(self, moves, board):
        """将一系列移动转换为PGN格式
        
        Args:
            moves: 包含移动和玩家的列表 [(move, player), ...]
            board: 棋盘对象
            
        Returns:
            完整的PGN格式字符串
        """
        pgn = self.create_pgn_header()
        move_num = 1
        
        # 创建一个新的棋盘对象用于模拟移动
        sim_board = Board()
        
        for i, (move, player) in enumerate(moves):
            from_pos, to_pos = move
            from_x, from_y = from_pos
            to_x, to_y = to_pos
            
            # 从模拟棋盘获取棋子
            piece = sim_board.get_piece(from_x, from_y)
            if not piece:
                continue  # 跳过无效移动
                
            # 执行模拟移动以更新模拟棋盘状态
            sim_board.move_piece(from_pos, to_x, to_y, player)
            
            notation = self.move_to_pgn_notation(move, sim_board, player)
            
            if i % 2 == 0:  # 红方移动
                pgn += f"{move_num}. {notation} "
            else:  # 黑方移动
                pgn += f"{notation}\n"
                move_num += 1
                
        # 确保最后一行有结束符
        if len(moves) % 2 == 1:
            pgn += "\n"
            
        pgn += " *\n"
        return pgn
    
    def save_pgn_file(self, pgn_content, filepath=None):
        """保存PGN内容到文件
        
        Args:
            pgn_content: PGN格式的字符串
            filepath: 可选的文件路径，如果未提供则弹出对话框
            
        Returns:
            保存的文件路径
        """
        if not filepath:
            # 默认文件名
            default_filename = f"game_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pgn"
            
            # 创建一个临时的根窗口
            root = tk.Tk()
            root.withdraw()  # 隐藏根窗口
            
            # 显示文件保存对话框
            filepath = filedialog.asksaveasfilename(
                title="保存棋谱文件",
                defaultextension=".pgn",
                initialfile=default_filename,
                filetypes=[("PGN文件", "*.pgn"), ("所有文件", "*.*")]
            )
            
            # 销毁根窗口
            root.destroy()
            
            if not filepath:
                return None
        
        # 保存文件
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(pgn_content)
            return filepath
        except Exception as e:
            print(f"保存PGN文件失败: {e}")
            return None
    
    def parse_pgn_file(self, filepath):
        """解析PGN文件并提取移动
        
        Args:
            filepath: PGN文件路径
            
        Returns:
            包含移动和玩家的列表 [(move, player), ...]
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 跳过头部信息
            moves_section = content.split('\n\n')[-1]
            
            # 提取移动
            moves = []
            move_pattern = r'(\d+)\.\s+([^\s]+)\s+([^\s]+)'
            
            # 简单解析（可以根据实际PGN格式进行扩展）
            lines = moves_section.strip().split('\n')
            for line in lines:
                if not line or line.strip() == '*':
                    continue
                    
                parts = line.split()
                move_index = 0
                player = 0
                
                for part in parts:
                    if part.endswith('.'):
                        # 这是回合数
                        move_index = int(part[:-1])
                        player = 0
                    elif part != '*':
                        # 这是一个移动
                        # 简单解析 "from_x,from_y-to_x,to_y" 格式
                        # 这里需要根据实际PGN记谱方式调整
                        if '-' in part:
                            from_str, to_str = part.split('-')
                            from_x, from_y = int(from_str[0]), int(from_str[1])
                            to_x, to_y = int(to_str[0]), int(to_str[1])
                            
                            move = ((from_x, from_y), (to_x, to_y))
                            moves.append((move, player))
                            
                            # 切换玩家
                            player = 1 - player
            
            return moves
        except Exception as e:
            print(f"解析PGN文件失败: {e}")
            return []