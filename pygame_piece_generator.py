#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pygame
import os
import sys

# 初始化pygame
pygame.init()

def create_piece_image(text, size=(73, 73), is_red=False):
    """创建中国象棋棋子图片"""
    # 创建一个透明表面
    surface = pygame.Surface(size, pygame.SRCALPHA)
    
    # 设置背景颜色 (红色或黑色)
    bg_color = (180, 0, 0) if is_red else (0, 0, 0)
    
    # 绘制圆形(使用pygame.gfxdraw可以获得抗锯齿效果，但这里用普通的绘制)
    pygame.draw.circle(surface, bg_color, (size[0]//2, size[1]//2), size[0]//2 - 1)
    pygame.draw.circle(surface, (255, 255, 255), (size[0]//2, size[1]//2), size[0]//2 - 1, 2)
    
    # 为不同的棋子绘制不同标识符
    center = (size[0]//2, size[1]//2)
    radius = min(size[0], size[1]) // 4
    
    # 根据棋子类型绘制不同的形状
    if text in ["帅", "将"]:  # 将/帅
        pygame.draw.rect(surface, (255, 255, 255), (center[0]-radius, center[1]-radius, radius*2, radius*2), 2)
    elif text in ["士", "仕"]:  # 士/仕
        pygame.draw.line(surface, (255, 255, 255), (center[0]-radius, center[1]-radius), (center[0]+radius, center[1]+radius), 2)
        pygame.draw.line(surface, (255, 255, 255), (center[0]-radius, center[1]+radius), (center[0]+radius, center[1]-radius), 2)
    elif text in ["象", "相"]:  # 象/相
        pygame.draw.circle(surface, (255, 255, 255), center, radius, 2)
    elif text in ["马"]:  # 马
        points = [
            (center[0], center[1]-radius),
            (center[0]+radius, center[1]),
            (center[0], center[1]+radius),
            (center[0]-radius, center[1])
        ]
        pygame.draw.polygon(surface, (255, 255, 255), points, 2)
    elif text in ["车"]:  # 车
        pygame.draw.rect(surface, (255, 255, 255), (center[0]-radius, center[1]-radius, radius*2, radius*2), 2)
    elif text in ["炮"]:  # 炮
        pygame.draw.circle(surface, (255, 255, 255), center, radius, 2)
        pygame.draw.circle(surface, (255, 255, 255), center, radius//2, 0)
    elif text in ["兵", "卒"]:  # 兵/卒
        pygame.draw.circle(surface, (255, 255, 255), center, radius//2, 0)
    
    # 添加文本（如果能加载中文字体）
    try:
        # 尝试加载中文字体
        font_paths = [
            "C:/Windows/Fonts/simhei.ttf",
            "C:/Windows/Fonts/simsun.ttc",
            "C:/Windows/Fonts/msyh.ttc"
        ]
        
        font = None
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    font = pygame.font.Font(font_path, 36)
                    break
                except:
                    continue
        
        if font:
            text_surface = font.render(text, True, (255, 255, 255))
            text_rect = text_surface.get_rect(center=center)
            surface.blit(text_surface, text_rect)
    except Exception as e:
        print(f"无法添加文本: {e}")
    
    return surface

def generate_all_pieces():
    """生成所有棋子图片"""
    # 确保输出目录存在
    output_dir = "assets/images/pieces"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")
    
    # 定义棋子
    red_pieces = {
        "general": "帅",
        "advisor": "士", 
        "elephant": "相",
        "horse": "马",
        "chariot": "车",
        "cannon": "炮",
        "soldier": "兵"
    }
    
    black_pieces = {
        "general": "将",
        "advisor": "仕",
        "elephant": "象",
        "horse": "马",
        "chariot": "车", 
        "cannon": "炮",
        "soldier": "卒"
    }
    
    # 生成红方棋子
    for piece_type, text in red_pieces.items():
        filename = os.path.join(output_dir, f"red_{piece_type}.png")
        surface = create_piece_image(text, is_red=True)
        try:
            pygame.image.save(surface, filename)
            print(f"已生成红方{text}棋子图片: {filename}")
        except Exception as e:
            print(f"保存红方{text}棋子图片失败: {e}")
    
    # 生成黑方棋子
    for piece_type, text in black_pieces.items():
        filename = os.path.join(output_dir, f"black_{piece_type}.png")
        surface = create_piece_image(text, is_red=False)
        try:
            pygame.image.save(surface, filename)
            print(f"已生成黑方{text}棋子图片: {filename}")
        except Exception as e:
            print(f"保存黑方{text}棋子图片失败: {e}")
    
    print("所有棋子图片生成完毕！")

if __name__ == "__main__":
    print("开始生成中国象棋棋子图片...")
    generate_all_pieces()
    pygame.quit()
