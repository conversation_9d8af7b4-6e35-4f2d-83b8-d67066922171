import pygame
import os
from constants import *
from piece import Piece
from general import General
from rules import Rules


def create_piece(piece_type, x, y, player):
    if piece_type == "general":
        return General(x, y, player)
    else:
        # 为简化代码，其他棋子类型暂时使用通用Piece类
        return Piece(x, y, player, piece_type)


class Board:
    def __init__(self):
        # 加载棋盘图片
        board_path = os.path.join("assets", "images", "board.png")
        try:
            self.board_image = pygame.image.load(board_path)
            # 获取原始图片尺寸
            original_width, original_height = self.board_image.get_size()
            # 添加width和height属性
            self.width = original_width
            self.height = original_height
            # 更新常量中的棋盘尺寸以匹配原始图片
            global BOARD_WIDTH, BOARD_HEIGHT, GRID_WIDTH, GRID_HEIGHT, PIECE_RADIUS
            BOARD_WIDTH = original_width
            BOARD_HEIGHT = original_height
            # 不再重新计算偏移量，使用constants.py中设置的值
            # 根据原始图片尺寸调整网格大小
            GRID_WIDTH = BOARD_WIDTH / 9
            GRID_HEIGHT = BOARD_HEIGHT / 10
            # 根据网格大小调整棋子尺寸，使棋子之间只有0.5毫米的间距
            # 计算0.5毫米在屏幕上的像素数（假设屏幕DPI为96，1英寸=25.4毫米）
            gap_pixels = 0.5 * 96 / 25.4
            # 棋子半径 = (网格宽度 - 间距) / 2
            PIECE_RADIUS = min(GRID_WIDTH, GRID_HEIGHT) / 2 - gap_pixels / 2
        except:
            # 如果图片加载失败，创建一个简单的棋盘背景
            self.board_image = pygame.Surface((BOARD_WIDTH, BOARD_HEIGHT))
            self.board_image.fill((240, 217, 181))  # 木色背景
            # 确保width和height属性也在加载失败时被设置
            self.width = BOARD_WIDTH
            self.height = BOARD_HEIGHT

        # 初始化棋盘状态 (9x10网格)
        self.grid = [[None for _ in range(9)] for _ in range(10)]

        # 棋盘翻转标志
        self.flipped = False

        # 初始化棋子
        self.reset()

    def reset(self):
        # 清空棋盘
        for i in range(10):
            for j in range(9):
                self.grid[i][j] = None

        # 放置初始棋子
        # 黑方(下方，1号玩家，y=9,8,7,6)
        # 底线棋子
        self.place_piece("chariot", 0, 9, BLACK_PLAYER)
        self.place_piece("horse", 1, 9, BLACK_PLAYER)
        self.place_piece("elephant", 2, 9, BLACK_PLAYER)
        self.place_piece("advisor", 3, 9, BLACK_PLAYER)
        self.place_piece("general", 4, 9, BLACK_PLAYER)
        self.place_piece("advisor", 5, 9, BLACK_PLAYER)
        self.place_piece("elephant", 6, 9, BLACK_PLAYER)
        self.place_piece("horse", 7, 9, BLACK_PLAYER)
        self.place_piece("chariot", 8, 9, BLACK_PLAYER)
        # 炮
        self.place_piece("cannon", 1, 7, BLACK_PLAYER)
        self.place_piece("cannon", 7, 7, BLACK_PLAYER)
        # 卒
        self.place_piece("soldier", 0, 6, BLACK_PLAYER)
        self.place_piece("soldier", 2, 6, BLACK_PLAYER)
        self.place_piece("soldier", 4, 6, BLACK_PLAYER)
        self.place_piece("soldier", 6, 6, BLACK_PLAYER)
        self.place_piece("soldier", 8, 6, BLACK_PLAYER)

        # 红方(上方，0号玩家，y=0,1,2,3)
        # 底线棋子
        self.place_piece("chariot", 0, 0, RED_PLAYER)
        self.place_piece("horse", 1, 0, RED_PLAYER)
        self.place_piece("elephant", 2, 0, RED_PLAYER)
        self.place_piece("advisor", 3, 0, RED_PLAYER)
        self.place_piece("general", 4, 0, RED_PLAYER)
        self.place_piece("advisor", 5, 0, RED_PLAYER)
        self.place_piece("elephant", 6, 0, RED_PLAYER)
        self.place_piece("horse", 7, 0, RED_PLAYER)
        self.place_piece("chariot", 8, 0, RED_PLAYER)
        # 炮
        self.place_piece("cannon", 1, 2, RED_PLAYER)
        self.place_piece("cannon", 7, 2, RED_PLAYER)
        # 兵
        self.place_piece("soldier", 0, 3, RED_PLAYER)
        self.place_piece("soldier", 2, 3, RED_PLAYER)
        self.place_piece("soldier", 4, 3, RED_PLAYER)
        self.place_piece("soldier", 6, 3, RED_PLAYER)
        self.place_piece("soldier", 8, 3, RED_PLAYER)

    def place_piece(self, piece_type, x, y, player):
        self.grid[y][x] = create_piece(piece_type, x, y, player)

    def get_piece(self, x, y):
        """获取指定位置的棋子"""
        # 确保x和y是整数
        x, y = int(x), int(y)
        # 检查坐标是否在棋盘范围内
        if 0 <= x < 9 and 0 <= y < 10:
            return self.grid[y][x]
        return None

    def to_fen(self):
        """将当前棋盘状态转换为FEN字符串"""
        fen = ""

        # 定义FEN中棋子的字符表示
        piece_symbols = {
            0: {  # 红方（大写字母）
                "general": "K",
                "advisor": "A",
                "elephant": "B",
                "horse": "N",
                "chariot": "R",
                "cannon": "C",
                "soldier": "P"
            },
            1: {  # 黑方（小写字母）
                "general": "k",
                "advisor": "a",
                "elephant": "b",
                "horse": "n",
                "chariot": "r",
                "cannon": "c",
                "soldier": "p"
            }
        }

        # 遍历棋盘，构建FEN字符串
        for y in range(10):
            empty_count = 0

            for x in range(9):
                piece = self.grid[y][x]

                if piece is None:
                    empty_count += 1
                else:
                    # 如果之前有空格，先记录空格数量
                    if empty_count > 0:
                        fen += str(empty_count)
                        empty_count = 0

                    # 添加棋子符号
                    if hasattr(piece, 'piece_type') and piece.piece_type in piece_symbols[piece.player]:
                        fen += piece_symbols[piece.player][piece.piece_type]
                    else:
                        # 如果找不到对应的棋子类型，使用通用符号
                        fen += "X" if piece.player == 0 else "x"

            # 如果行末有空格，记录空格数量
            if empty_count > 0:
                fen += str(empty_count)

            # 除了最后一行，每行用"/"分隔
            if y < 9:
                fen += "/"

        # 添加当前玩家（假设红方为'w'，黑方为'b'）
        # fen += " w "  # 这里省略了其他FEN组件，仅保留棋盘状态

        return fen

    def get_grid_position(self, pos):
        # 将屏幕坐标转换为棋盘网格坐标
        x, y = pos
        x = int((x - BOARD_OFFSET_X) / GRID_WIDTH)

        # 修改坐标系统，使y坐标从左下角开始向上计数
        screen_y = int((y - BOARD_OFFSET_Y) / GRID_HEIGHT)
        y = 9 - screen_y  # 翻转y坐标，0对应底部，9对应顶部

        # 如果棋盘已翻转，调整坐标
        if self.flipped:
            x = 8 - x  # 水平翻转
            y = 9 - y  # 垂直翻转

        if 0 <= x < 9 and 0 <= y < 10:
            return (x, y)
        return None

    def get_screen_position(self, grid_pos):
        # 将棋盘网格坐标转换为屏幕坐标
        x, y = grid_pos

        # 如果棋盘已翻转，调整坐标
        if self.flipped:
            x = 8 - x  # 水平翻转
            y = 9 - y  # 垂直翻转

        # 翻转y坐标，把"左下角原点"转换回"左上角原点"用于屏幕显示
        screen_y = 9 - y

        screen_x = BOARD_OFFSET_X + x * GRID_WIDTH + GRID_WIDTH // 2
        screen_y = BOARD_OFFSET_Y + screen_y * GRID_HEIGHT + GRID_HEIGHT // 2
        return (screen_x, screen_y)

    def get_piece_screen_position(self, x, y):
        """将棋子的棋盘坐标转换为屏幕显示坐标（中心点）"""
        # 翻转y坐标，把"左下角原点"转换回"左上角原点"用于屏幕显示
        screen_y = 9 - y

        screen_x = BOARD_OFFSET_X + x * GRID_WIDTH + GRID_WIDTH // 2
        screen_y = BOARD_OFFSET_Y + screen_y * GRID_HEIGHT + GRID_HEIGHT // 2
        return (screen_x, screen_y)

    def move_piece(self, from_pos, to_x, to_y, current_player):
        """移动棋子，并检查移动是否合法"""
        from_x, from_y = from_pos
        piece = self.get_piece(from_x, from_y)

        if not piece or piece.player != current_player:
            return False

        # 检查移动是否合法
        if not piece.is_valid_move(to_x, to_y, self):
            return False

        # 执行移动
        captured_piece = self.grid[to_y][to_x]
        self.grid[to_y][to_x] = piece
        self.grid[from_y][from_x] = None
        piece.x, piece.y = to_x, to_y

        # 检查将帅是否面对面
        if Rules.is_general_face_to_face(self):
            # 撤销移动
            self.grid[from_y][from_x] = piece
            self.grid[to_y][to_x] = captured_piece
            piece.x, piece.y = from_x, from_y
            return False

        # 检查移动后是否导致自己被将军
        player_side = 'red' if piece.player == RED_PLAYER else 'black'
        if self.is_in_check(player_side):
            # 撤销移动
            self.grid[from_y][from_x] = piece
            self.grid[to_y][to_x] = captured_piece
            piece.x, piece.y = from_x, from_y
            return False

        return True

    def is_in_check(self, side):
        """
        检测指定方是否被将军
        :param side: 'red'或'black'
        """
        # 1. 找到当前方的将/帅
        general_pos = None
        player = RED_PLAYER if side == 'red' else BLACK_PLAYER

        # 优化：只在九宫格内查找将帅
        if player == RED_PLAYER:  # 红方
            search_range_y = range(0, 3)
        else:  # 黑方
            search_range_y = range(7, 10)

        for x in range(3, 6):
            for y in search_range_y:
                piece = self.grid[y][x]
                if piece and piece.piece_type == "general" and piece.player == player:
                    general_pos = (x, y)
                    break
            if general_pos:
                break

        if not general_pos:
            return False  # 将帅不在棋盘上（异常情况）

        # 2. 检查对方所有棋子能否攻击到将帅
        opponent_player = BLACK_PLAYER if side == 'red' else RED_PLAYER

        # 优化：只检查对方棋子
        for y in range(10):
            for x in range(9):
                piece = self.grid[y][x]
                if piece and piece.player == opponent_player:
                    # 直接使用is_valid_move检查
                    if piece.is_valid_move(general_pos[0], general_pos[1], self):
                        return True

        return False

    def is_checkmate(self, player):
        # 如果不在将军状态，不可能被将死
        player_side = 'red' if player == RED_PLAYER else 'black'
        if not self.is_in_check(player_side):
            return False

        # 检查所有可能的移动
        for y in range(10):
            for x in range(9):
                piece = self.grid[y][x]
                if piece and piece.player == player:
                    # 尝试所有可能的移动
                    for to_y in range(10):
                        for to_x in range(9):
                            if piece.is_valid_move(to_x, to_y, self):
                                # 临时移动
                                original_pos = (piece.x, piece.y)
                                captured_piece = self.grid[to_y][to_x]

                                self.grid[to_y][to_x] = piece
                                self.grid[original_pos[1]
                                          ][original_pos[0]] = None
                                piece.x, piece.y = to_x, to_y

                                # 检查移动后是否仍然被将军
                                still_in_check = self.is_in_check(player_side)

                                # 撤销移动
                                self.grid[original_pos[1]
                                          ][original_pos[0]] = piece
                                self.grid[to_y][to_x] = captured_piece
                                piece.x, piece.y = original_pos

                                if not still_in_check:
                                    return False  # 找到一个可以解除将军的移动

        return True  # 没有找到可以解除将军的移动，将死

    def get_board_state(self):
        """获取棋盘状态的副本，用于AI分析"""
        # 创建一个新的Board对象作为当前状态的副本
        from piece import Piece
        from general import General

        board_copy = Board()

        # 清空新棋盘的所有棋子
        for y in range(10):
            for x in range(9):
                board_copy.grid[y][x] = None

        # 复制当前棋盘的所有棋子
        for y in range(10):
            for x in range(9):
                piece = self.grid[y][x]
                if piece:
                    # 创建相同类型的棋子
                    if piece.__class__.__name__ == "General":
                        new_piece = General(x, y, piece.player)
                    else:
                        new_piece = Piece(x, y, piece.player, piece.piece_type)
                    board_copy.grid[y][x] = new_piece

        return board_copy

    def render(self, screen, selected_piece):
        # 绘制棋盘
        screen.blit(self.board_image, (BOARD_OFFSET_X, BOARD_OFFSET_Y))

        # 绘制坐标刻度
        font = pygame.font.SysFont("arial", 16)

        # 横坐标a-i (对应0-8)，只在底部显示
        alpha_coords = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i']
        for x in range(9):
            # 只在底部显示横坐标
            text = font.render(alpha_coords[x], True, (0, 0, 0))
            pos_x = BOARD_OFFSET_X + x * GRID_WIDTH + \
                GRID_WIDTH // 2 - text.get_width() // 2
            pos_y = BOARD_OFFSET_Y + BOARD_HEIGHT + 5
            screen.blit(text, (pos_x, pos_y))

        # 纵坐标0-9，只在左侧显示，从下向上标注
        for y in range(10):
            # 只在左侧显示纵坐标，从下向上计数
            text = font.render(str(9-y), True, (0, 0, 0))  # 9-y 使得从下向上是0-9
            pos_x = BOARD_OFFSET_X - 20
            pos_y = BOARD_OFFSET_Y + y * GRID_HEIGHT + \
                GRID_HEIGHT // 2 - text.get_height() // 2
            screen.blit(text, (pos_x, pos_y))

        # 预先计算所有可移动位置，避免重复计算
        valid_moves = []

        # 只有当有选中的棋子时才计算有效移动
        if selected_piece:
            # 确保selected_piece是有效的棋子对象
            if isinstance(selected_piece, tuple):
                x, y = selected_piece
                piece = self.get_piece(x, y)
                if piece:  # 只有当坐标上有棋子时才继续
                    selected_piece = piece
                else:
                    selected_piece = None  # 如果坐标上没有棋子，则不选中任何棋子

            # 如果selected_piece是有效的棋子对象，计算其有效移动
            if selected_piece and hasattr(selected_piece, 'x') and hasattr(selected_piece, 'y'):
                # 预先计算所有可移动位置
                for y_pos in range(10):
                    for x_pos in range(9):
                        if selected_piece.is_valid_move(x_pos, y_pos, self):
                            # 临时检查移动是否会导致被将军
                            original_pos = (selected_piece.x, selected_piece.y)
                            captured_piece = self.grid[y_pos][x_pos]

                            self.grid[y_pos][x_pos] = selected_piece
                            self.grid[original_pos[1]][original_pos[0]] = None
                            selected_piece.x, selected_piece.y = x_pos, y_pos

                            # 检查将帅是否面对面
                            face_to_face = Rules.is_general_face_to_face(self)

                            # 检查是否被将军
                            player_side = 'red' if selected_piece.player == RED_PLAYER else 'black'
                            valid_move = not self.is_in_check(
                                player_side) and not face_to_face

                            # 撤销移动
                            self.grid[original_pos[1]
                                      ][original_pos[0]] = selected_piece
                            self.grid[y_pos][x_pos] = captured_piece
                            selected_piece.x, selected_piece.y = original_pos

                            if valid_move:
                                valid_moves.append((x_pos, y_pos))

        # 绘制棋子（先绘制所有棋子，确保它们在高亮和指示点之下）
        for y in range(10):
            for x in range(9):
                piece = self.grid[y][x]
                if piece:
                    # 获取棋子的屏幕位置
                    screen_pos = self.get_screen_position((x, y))
                    # 绘制棋子
                    piece.render(screen, screen_pos)

        # 绘制可移动位置的指示点
        for x_pos, y_pos in valid_moves:
            pos = self.get_screen_position((x_pos, y_pos))
            # 绘制可移动位置的指示点
            pygame.draw.circle(screen, GREEN, pos, 5)

        # 在棋子绘制后绘制选中高亮，确保轮廓可见
        if selected_piece and hasattr(selected_piece, 'x') and hasattr(selected_piece, 'y'):
            # 高亮选中的棋子（只高亮当前选中的一个棋子）
            pos = self.get_screen_position(
                (selected_piece.x, selected_piece.y))
            # 不管是红方还是黑方棋子，都使用相同的高亮颜色
            pygame.draw.circle(screen, HIGHLIGHT, pos, PIECE_RADIUS + 2, 2)
