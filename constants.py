# constants.py

# 游戏状态
STATE_MENU = 0
STATE_PLAYING = 1
STATE_GAME_OVER = 2

# 玩家
RED_PLAYER = 0
BLACK_PLAYER = 1

# 窗口尺寸
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 840  # 增加窗口高度（原来是810）

# 工具栏尺寸
TOOLBAR_HEIGHT = 40

# 窗口标题
TITLE = "中国象棋"

# 棋盘尺寸
BOARD_WIDTH = 521  # 棋盘图片的宽度
BOARD_HEIGHT = 577  # 棋盘图片的高度

# 棋盘偏移 (用于调整棋盘在窗口中的位置)
BOARD_OFFSET_X = 20
BOARD_OFFSET_Y = 20 + TOOLBAR_HEIGHT

# 棋盘网格单元大小
GRID_WIDTH = BOARD_WIDTH / 9
GRID_HEIGHT = BOARD_HEIGHT / 10

# 棋子半径
PIECE_RADIUS = min(GRID_WIDTH, GRID_HEIGHT) / 2 - 2

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
HIGHLIGHT = (255, 215, 0)  # 高亮选中的棋子的颜色

# 音效相关常量
SOUND_MOVE = "move.wav"
SOUND_CAPTURE = "capture.wav"
SOUND_CHECK = "check.wav"
SOUND_START = "start.wav"