class Rules:
    @staticmethod
    def is_in_board(x, y):
        """检查坐标是否在棋盘内"""
        return 0 <= x < 9 and 0 <= y < 10

    @staticmethod
    def is_in_palace(x, y, player):
        """检查坐标是否在指定玩家的九宫格内"""
        if player == 0:  # 红方
            return 3 <= x <= 5 and 0 <= y <= 2
        else:  # 黑方
            return 3 <= x <= 5 and 7 <= y <= 9

    @staticmethod
    def is_across_river(y, player):
        """检查是否过河"""
        if player == 0:  # 红方（现在在上方y=0,1,2,3）
            return y > 4
        else:  # 黑方（现在在下方y=6,7,8,9）
            return y < 5

    @staticmethod
    def get_pieces_between(from_x, from_y, to_x, to_y, board):
        """获取两点之间的所有棋子（不包括起点和终点）"""
        pieces = []

        # 只处理直线移动
        if from_x != to_x and from_y != to_y:
            return pieces

        if from_x == to_x:  # 垂直移动
            start = min(from_y, to_y) + 1
            end = max(from_y, to_y)
            for y in range(start, end):
                piece = board.get_piece(from_x, y)
                if piece:
                    pieces.append(piece)
        else:  # 水平移动
            start = min(from_x, to_x) + 1
            end = max(from_x, to_x)
            for x in range(start, end):
                piece = board.get_piece(x, from_y)
                if piece:
                    pieces.append(piece)

        return pieces

    @staticmethod
    def is_horse_blocked(from_x, from_y, to_x, to_y, board):
        """检查马的移动是否被阻挡（蹩马腿）"""
        # 马走"日"字，先横后竖或先竖后横
        dx = abs(to_x - from_x)
        dy = abs(to_y - from_y)

        if not ((dx == 1 and dy == 2) or (dx == 2 and dy == 1)):
            return False  # 不是马的移动

        # 检查阻挡点
        if dx == 1:  # 先横后竖
            block_x = from_x
            block_y = from_y + (1 if to_y > from_y else -1)
        else:  # 先竖后横
            block_x = from_x + (1 if to_x > from_x else -1)
            block_y = from_y

        return board.get_piece(block_x, block_y) is not None

    @staticmethod
    def is_cannon_jump_valid(from_x, from_y, to_x, to_y, board):
        """检查炮的跳跃是否有效（中间有且只有一个棋子）"""
        # 炮必须直线移动
        if from_x != to_x and from_y != to_y:
            return False

        pieces_between = Rules.get_pieces_between(
            from_x, from_y, to_x, to_y, board)
        target_piece = board.get_piece(to_x, to_y)

        # 炮移动规则：
        # 1. 如果目标位置为空，则中间不能有任何棋子
        # 2. 如果目标位置有棋子，则中间必须有且只有一个棋子
        if target_piece is None:
            return len(pieces_between) == 0
        else:
            return len(pieces_between) == 1

    @staticmethod
    def is_general_face_to_face(board):
        """检查将帅是否面对面"""
        red_general = None
        black_general = None
        
        # 找到双方的将帅
        for x in range(3, 6):
            for y in range(0, 3):
                piece = board.get_piece(x, y)
                # 修改这里: name -> piece_type
                if piece and piece.piece_type == "general" and piece.player == 0:
                    red_general = (x, y)
            
            for y in range(7, 10):
                piece = board.get_piece(x, y)
                # 修改这里: name -> piece_type
                if piece and piece.piece_type == "general" and piece.player == 1:
                    black_general = (x, y)

        if not (red_general and black_general):
            return False

        # 检查是否在同一列
        if red_general[0] != black_general[0]:
            return False

        # 检查中间是否有棋子
        pieces_between = Rules.get_pieces_between(
            red_general[0], red_general[1],
            black_general[0], black_general[1],
            board
        )

        return len(pieces_between) == 0
