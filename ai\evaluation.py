# -*- coding: utf-8 -*-

import numpy as np

class Evaluation:
    """评估棋盘局面的类"""
    
    def __init__(self):
        # 棋子基础价值
        self.piece_values = {
            'general': 10000,  # 将/帅
            'advisor': 200,    # 士/仕
            'elephant': 200,   # 象/相
            'horse': 400,      # 马
            'chariot': 900,    # 车
            'cannon': 450,     # 炮
            'soldier': 100     # 兵/卒
        }
        
        # 位置加成表
        self.position_tables = self.init_position_tables()
    
    def init_position_tables(self):
        """初始化各类棋子的位置价值表"""
        tables = {}
        
        # 兵/卒位置价值表
        soldier_table = np.zeros((10, 9))
        # 过河后价值提升
        for y in range(5):
            for x in range(9):
                soldier_table[y, x] = 20
        # 中间列价值更高
        for y in range(10):
            soldier_table[y, 4] += 10
        # 前进越多价值越高
        for y in range(10):
            soldier_table[y] += (9 - y) * 5
        tables['soldier'] = soldier_table
        
        # 炮位置价值表
        cannon_table = np.zeros((10, 9))
        # 中间位置有利
        for y in range(2, 8):
            for x in range(2, 7):
                cannon_table[y, x] = 10
        # 炮架位置
        cannon_table[2, 1] = 20
        cannon_table[2, 7] = 20
        cannon_table[7, 1] = 20
        cannon_table[7, 7] = 20
        tables['cannon'] = cannon_table
        
        # 车位置价值表
        chariot_table = np.zeros((10, 9))
        # 控制中路
        for x in range(9):
            chariot_table[0, x] = 10
            chariot_table[9, x] = 10
        for y in range(10):
            chariot_table[y, 0] = 10
            chariot_table[y, 8] = 10
        tables['chariot'] = chariot_table
        
        # 马位置价值表
        horse_table = np.zeros((10, 9))
        # 中心位置价值高
        for y in range(3, 7):
            for x in range(2, 7):
                horse_table[y, x] = 15
        # 马角位置
        horse_table[2, 1] = 10
        horse_table[2, 7] = 10
        horse_table[7, 1] = 10
        horse_table[7, 7] = 10
        tables['horse'] = horse_table
        
        # 象/相位置价值表
        elephant_table = np.zeros((10, 9))
        # 象眼位置
        elephant_table[0, 2] = 10
        elephant_table[0, 6] = 10
        elephant_table[2, 0] = 10
        elephant_table[2, 4] = 20
        elephant_table[2, 8] = 10
        elephant_table[4, 2] = 10
        elephant_table[4, 6] = 10
        # 黑方象眼位置
        elephant_table[9, 2] = 10
        elephant_table[9, 6] = 10
        elephant_table[7, 0] = 10
        elephant_table[7, 4] = 20
        elephant_table[7, 8] = 10
        elephant_table[5, 2] = 10
        elephant_table[5, 6] = 10
        tables['elephant'] = elephant_table
        
        # 士/仕位置价值表
        advisor_table = np.zeros((10, 9))
        # 士的有效位置
        advisor_table[0, 3] = 10
        advisor_table[0, 5] = 10
        advisor_table[1, 4] = 20
        advisor_table[2, 3] = 10
        advisor_table[2, 5] = 10
        # 黑方士的有效位置
        advisor_table[9, 3] = 10
        advisor_table[9, 5] = 10
        advisor_table[8, 4] = 20
        advisor_table[7, 3] = 10
        advisor_table[7, 5] = 10
        tables['advisor'] = advisor_table
        
        # 将/帅位置价值表
        general_table = np.zeros((10, 9))
        # 将的有效位置，中间位置最佳
        general_table[0, 4] = 30
        general_table[1, 3] = 10
        general_table[1, 4] = 20
        general_table[1, 5] = 10
        general_table[2, 3] = 10
        general_table[2, 4] = 20
        general_table[2, 5] = 10
        # 黑方将的有效位置
        general_table[9, 4] = 30
        general_table[8, 3] = 10
        general_table[8, 4] = 20
        general_table[8, 5] = 10
        general_table[7, 3] = 10
        general_table[7, 4] = 20
        general_table[7, 5] = 10
        tables['general'] = general_table
        
        return tables
    
    def evaluate(self, board, player):
        """评估棋盘局面，返回对于当前玩家的评分"""
        score = 0
        
        # 计算棋子基本价值和位置价值
        for y in range(10):
            for x in range(9):
                piece = board.get_piece(x, y)
                if piece:
                    # 基础棋子价值
                    piece_value = self.piece_values.get(piece.piece_type, 0)
                    
                    # 位置加成
                    position_table = self.position_tables.get(piece.piece_type, np.zeros((10, 9)))
                    position_value = position_table[y, x]
                    
                    # 根据棋子所属方向调整评分
                    if piece.player == player:
                        score += piece_value + position_value
                    else:
                        score -= piece_value + position_value
        
        # 新增：评估棋子间配合
        score += self.evaluate_piece_coordination(board, player)
        
        # 新增：评估局面控制
        score += self.evaluate_board_control(board, player)
        
        # 检查将军状态
        if board.is_in_check(1 - player):
            score += 50  # 如果对手被将军，加分
        
        if board.is_in_check(player):
            score -= 50  # 如果自己被将军，减分
        
        # 检查将死状态
        if board.is_checkmate(1 - player):
            score = 100000  # 如果对手被将死，最高分
        
        if board.is_checkmate(player):
            score = -100000  # 如果自己被将死，最低分
        
        return score
    
    def evaluate_piece_coordination(self, board, player):
        """评估棋子间配合"""
        coordination_score = 0
        pieces = []
        for y in range(10):
            for x in range(9):
                piece = board.get_piece(x, y)
                if piece and piece.player == player:
                    pieces.append(piece)
    
        for i in range(len(pieces)):
            for j in range(i + 1, len(pieces)):
                if self.is_piece_supporting(pieces[i], pieces[j], board):
                    coordination_score += 50
    
        return coordination_score
    
    def is_piece_supporting(self, piece1, piece2, board):
        """检查两个棋子是否相互支持"""
        # 简单示例：如果两个棋子在同一行或同一列，且中间没有其他棋子，则认为相互支持
        if piece1.x == piece2.x:
            min_y = min(piece1.y, piece2.y)
            max_y = max(piece1.y, piece2.y)
            for y in range(min_y + 1, max_y):
                if board.get_piece(piece1.x, y):
                    return False
            return True
        elif piece1.y == piece2.y:
            min_x = min(piece1.x, piece2.x)
            max_x = max(piece1.x, piece2.x)
            for x in range(min_x + 1, max_x):
                if board.get_piece(x, piece1.y):
                    return False
            return True
        return False
    
    def evaluate_board_control(self, board, player):
        """评估局面控制"""
        control_score = 0
        controlled_squares = set()
    
        for y in range(10):
            for x in range(9):
                piece = board.get_piece(x, y)
                if piece and piece.player == player:
                    for to_y in range(10):
                        for to_x in range(9):
                            if piece.is_valid_move(to_x, to_y, board):
                                controlled_squares.add((to_x, to_y))
    
        control_score = len(controlled_squares) * 10
        return control_score
    
    def evaluate_move(self, board, move, player):
        """评估特定移动的价值"""
        from_pos, to_pos = move
        from_x, from_y = from_pos
        to_x, to_y = to_pos
        
        # 临时执行移动
        piece = board.get_piece(from_x, from_y)
        captured_piece = board.get_piece(to_x, to_y)
        
        # 临时移动
        original_pos = (piece.x, piece.y)
        board.grid[to_y][to_x] = piece
        board.grid[from_y][from_x] = None
        piece.x, piece.y = to_x, to_y
        
        # 评估移动后的局面
        score = self.evaluate(board, player)
        
        # 撤销移动
        board.grid[from_y][from_x] = piece
        board.grid[to_y][to_x] = captured_piece
        piece.x, piece.y = original_pos
        
        return score