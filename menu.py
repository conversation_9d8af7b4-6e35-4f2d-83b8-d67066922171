import pygame
import os
import sys
from constants import WINDOW_WIDTH, WINDOW_HEIGHT, STATE_PLAYING

class Button:
    def __init__(self, text, x, y, width, height, action=None):
        self.text = text
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.action = action
        
        # 使用更可靠的字体加载方式
        self.font = load_font(32)
        
        # 按钮颜色
        self.normal_color = (200, 200, 200)
        self.hover_color = (150, 150, 150)
        self.current_color = self.normal_color
    
    def is_clicked(self, pos):
        x, y = pos
        if self.x <= x <= self.x + self.width and self.y <= y <= self.y + self.height:
            return True
        return False
    
    def update(self, mouse_pos):
        x, y = mouse_pos
        if self.x <= x <= self.x + self.width and self.y <= y <= self.y + self.height:
            self.current_color = self.hover_color
        else:
            self.current_color = self.normal_color
    
    def render(self, screen):
        # 更新按钮状态
        self.update(pygame.mouse.get_pos())
        
        # 绘制按钮背景
        pygame.draw.rect(screen, self.current_color, (self.x, self.y, self.width, self.height))
        pygame.draw.rect(screen, (0, 0, 0), (self.x, self.y, self.width, self.height), 2)
        
        # 绘制按钮文本
        if self.font:
            text_surface = self.font.render(self.text, True, (0, 0, 0))
            text_rect = text_surface.get_rect(center=(self.x + self.width // 2, self.y + self.height // 2))
            screen.blit(text_surface, text_rect)
        else:
            # 如果字体加载失败，绘制一个简单的文本框
            pygame.draw.rect(screen, (0, 0, 0), (self.x + 10, self.y + self.height // 2 - 15, self.width - 20, 30), 0)
            pygame.draw.rect(screen, self.current_color, (self.x + 12, self.y + self.height // 2 - 13, self.width - 24, 26), 0)

# 字体缓存
_font_cache = {}

# 全局字体加载函数
def load_font(size):
    """加载支持中文的字体"""
    # 检查缓存
    if size in _font_cache:
        return _font_cache[size]
    
    # 首先尝试从fonts目录加载字体
    font_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts")
    font_path = os.path.join(font_dir, "simhei.ttf")
    
    if os.path.exists(font_path):
        try:
            print(f"正在加载字体: {font_path}, 大小: {size}")
            font = pygame.font.Font(font_path, size)
            # 测试字体是否能正确渲染中文
            test_text = font.render("中文测试", True, (0, 0, 0))
            if test_text.get_width() > 0:
                print("字体加载成功，可以渲染中文")
                _font_cache[size] = font
                return font
            else:
                print("字体加载成功，但无法渲染中文")
        except Exception as e:
            print(f"加载字体文件失败: {e}")
    else:
        print(f"字体文件不存在: {font_path}")
    
    # 如果无法从文件加载，尝试系统字体
    print("尝试使用系统字体...")
    chinese_fonts = ["SimHei", "Microsoft YaHei", "SimSun", "NSimSun", "FangSong", "KaiTi"]
    for font_name in chinese_fonts:
        try:
            font = pygame.font.SysFont(font_name, size)
            # 测试字体是否能正确渲染中文
            test_text = font.render("中文测试", True, (0, 0, 0))
            if test_text.get_width() > 0:
                print(f"系统字体 {font_name} 加载成功")
                _font_cache[size] = font
                return font
        except Exception as e:
            print(f"系统字体 {font_name} 加载失败: {e}")
            continue
    
    # 如果所有尝试都失败，使用默认字体
    print("所有中文字体加载失败，使用默认字体")
    try:
        # 使用默认字体
        font = pygame.font.Font(None, size)
        _font_cache[size] = font
        return font
    except:
        print("无法加载任何字体！")
        return None

class Menu:
    def __init__(self, game):
        self.game = game
        
        # 使用更可靠的字体加载方式
        self.font = load_font(48)
        
        # 创建按钮
        button_width = 200
        button_height = 50
        button_x = WINDOW_WIDTH // 2 - button_width // 2
        
        # 使用英文替代文本，以防中文无法显示
        self.use_english = not self.can_display_chinese()
        
        if self.use_english:
            print("使用英文菜单")
            self.buttons = [
                Button("Play vs AI", button_x, 250, button_width, button_height, self.start_ai_game),
                Button("Two Players", button_x, 320, button_width, button_height, self.start_two_player_game),
                Button("Exit", button_x, 390, button_width, button_height, self.quit_game)
            ]
        else:
            print("使用中文菜单")
            self.buttons = [
                Button("人机对战", button_x, 250, button_width, button_height, self.start_ai_game),
                Button("双人对战", button_x, 320, button_width, button_height, self.start_two_player_game),
                Button("退出游戏", button_x, 390, button_width, button_height, self.quit_game)
            ]
    
    def can_display_chinese(self):
        """测试是否能显示中文"""
        if not self.font:
            return False
        
        try:
            test_surface = self.font.render("中文测试", True, (0, 0, 0))
            # 检查渲染后的宽度是否合理
            width = test_surface.get_width()
            print(f"中文测试渲染宽度: {width}")
            return width > 50
        except Exception as e:
            print(f"中文渲染测试失败: {e}")
            return False
    
    def handle_event(self, event):
        # 处理菜单按钮点击
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            pos = pygame.mouse.get_pos()
            
            # 检查按钮点击
            for button in self.buttons:
                if button.is_clicked(pos):
                    button.action()
                    return True
            
            # 处理保存/加载棋谱点击
            if hasattr(self, 'game') and hasattr(self.game, 'state'):
                if self.game.state == STATE_PLAYING and len(self.game.move_history) > 0:
                    save_pgn_button = pygame.Rect(self.game.screen.get_width() - 300, 50, 250, 40)
                    if save_pgn_button.collidepoint(pos):
                        self.game.save_pgn_dialog()
                        return True
            
                load_pgn_button = pygame.Rect(self.game.screen.get_width() - 300, 100, 250, 40)
                if load_pgn_button.collidepoint(pos):
                    self.game.open_pgn_dialog()
                    return True
            
        # 处理键盘热键
        if event.type == pygame.KEYDOWN:
            # Ctrl+S: 保存棋谱
            if event.key == pygame.K_s and pygame.key.get_mods() & pygame.KMOD_CTRL:
                if hasattr(self, 'game') and hasattr(self.game, 'state'):
                    if self.game.state == STATE_PLAYING and len(self.game.move_history) > 0:
                        self.game.save_pgn_dialog()
                        return True
            
            # Ctrl+O: 打开棋谱
            if event.key == pygame.K_o and pygame.key.get_mods() & pygame.KMOD_CTRL:
                if hasattr(self, 'game'):
                    self.game.open_pgn_dialog()
                    return True
        
        return False
    
    def render(self, screen):
        # 绘制标题
        if self.font:
            title_text = "Chinese Chess" if self.use_english else "中国象棋"
            title = self.font.render(title_text, True, (0, 0, 0))
            title_rect = title.get_rect(center=(WINDOW_WIDTH // 2, 150))
            screen.blit(title, title_rect)
        else:
            # 如果字体加载失败，绘制一个简单的标题框
            pygame.draw.rect(screen, (0, 0, 0), (WINDOW_WIDTH // 2 - 100, 130, 200, 50), 1)
        
        # 添加保存/加载棋谱按钮
        if self.game.state == STATE_PLAYING and len(self.game.move_history) > 0:
            save_pgn_button = pygame.Rect(screen.get_width() - 300, 50, 250, 40)
            pygame.draw.rect(screen, (220, 220, 220), save_pgn_button)
            pygame.draw.rect(screen, (0, 0, 0), save_pgn_button, 2)
            
            save_text = self.font.render("保存棋谱 (Ctrl+S)", True, (0, 0, 0))
            save_text_rect = save_text.get_rect(center=save_pgn_button.center)
            screen.blit(save_text, save_text_rect)
        
        # 添加加载棋谱按钮
        load_pgn_button = pygame.Rect(screen.get_width() - 300, 100, 250, 40)
        pygame.draw.rect(screen, (220, 220, 220), load_pgn_button)
        pygame.draw.rect(screen, (0, 0, 0), load_pgn_button, 2)
        
        load_text = self.font.render("打开棋谱 (Ctrl+O)", True, (0, 0, 0))
        load_text_rect = load_text.get_rect(center=load_pgn_button.center)
        screen.blit(load_text, load_text_rect)
        
        # 绘制按钮
        for button in self.buttons:
            button.render(screen)
    
    def start_ai_game(self):
        self.game.start_game(vs_ai=True)
        # 设置电脑方为黑方(1)，这样玩家就控制红方(0)
        self.game.set_computer_side(1)  # BLACK_PLAYER = 1
    
    def start_two_player_game(self):
        self.game.start_game(vs_ai=False)
    
    def quit_game(self):
        pygame.quit()
        sys.exit()