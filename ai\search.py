import time
import random
from concurrent.futures import ThreadPoolExecutor, as_completed


class Search:
    """搜索算法类，负责查找最佳走法"""

    def __init__(self, evaluation):
        self.evaluation = evaluation
        self.transposition_table = {}  # 置换表
        self.killer_moves = {}         # 杀手着法表
        self.history_table = {}        # 历史启发表
        self.cache = {}               # 评估缓存
        self.start_time = 0           # 搜索开始时间
        self.max_time = 0             # 最大搜索时间
        self.nodes_searched = 0       # 搜索节点计数
        self.time_safety_margin = 0.9  # 时间安全边界
        self.time_check_frequency = 10000  # 检查时间频率

    def find_best_move(self, board, moves, player, depth, max_time):
        """查找最佳走法"""
        self.start_time = time.time()
        self.max_time = max_time
        self.nodes_searched = 0

        # 清理旧的表项
        self.clear_tables()

        # 使用迭代加深
        best_move = None
        best_value = float('-inf')
        actual_depth = 1

        try:
            # 并行搜索
            with ThreadPoolExecutor(max_workers=12) as executor:
                while actual_depth <= depth and not self.is_time_up():
                    futures = []
                    alpha = float('-inf')
                    beta = float('inf')

                    # 对每个可能的移动创建一个搜索任务
                    for move in moves:
                        future = executor.submit(
                            self.search_move,
                            board,
                            move,
                            player,
                            actual_depth,
                            alpha,
                            beta
                        )
                        futures.append((move, future))

                    # 收集搜索结果
                    current_best_move = None
                    current_best_value = float('-inf')

                    for move, future in futures:
                        try:
                            value = future.result()
                            if value > current_best_value:
                                current_best_value = value
                                current_best_move = move
                                alpha = max(alpha, value)
                        except Exception as e:
                            print(f"Error in search thread: {e}")

                    if current_best_value > best_value:
                        best_value = current_best_value
                        best_move = current_best_move

                    actual_depth += 1

                    # 输出搜索信息
                    print(
                        f"Depth {actual_depth-1} completed: best move = {best_move}, value = {best_value}")

        except TimeoutError:
            print("Search timeout")

        return best_move

    def search_move(self, board, move, player, depth, alpha, beta):
        """搜索单个移动的价值"""
        # 模拟移动
        from_pos, to_pos = move
        piece = board.get_piece(from_pos[0], from_pos[1])
        captured = board.get_piece(to_pos[0], to_pos[1])

        # 临时移动
        board.grid[to_pos[1]][to_pos[0]] = piece
        board.grid[from_pos[1]][from_pos[0]] = None
        piece.x, piece.y = to_pos

        # 递归搜索
        value = -self.pvs(board, depth-1, -beta, -alpha, 1-player)

        # 撤销移动
        board.grid[from_pos[1]][from_pos[0]] = piece
        board.grid[to_pos[1]][to_pos[0]] = captured
        piece.x, piece.y = from_pos

        return value

    def pvs(self, board, depth, alpha, beta, player):
        """主要变例搜索（Principal Variation Search）"""
        # 检查时间
        self.nodes_searched += 1
        if self.nodes_searched % self.time_check_frequency == 0 and self.is_time_up():
            raise TimeoutError()

        # 达到叶子节点
        if depth == 0:
            return self.quiescence_search(board, alpha, beta, player)

        # 检查重复局面
        board_hash = self.get_board_hash(board)
        if board_hash in self.transposition_table:
            stored_depth, stored_value, stored_flag = self.transposition_table[board_hash]
            if stored_depth >= depth:
                if stored_flag == 'exact':
                    return stored_value
                elif stored_flag == 'lower' and stored_value > alpha:
                    alpha = stored_value
                elif stored_flag == 'upper' and stored_value < beta:
                    beta = stored_value
                if alpha >= beta:
                    return stored_value

        # 生成所有可能的移动
        moves = []
        for y in range(10):
            for x in range(9):
                piece = board.get_piece(x, y)
                if piece and piece.player == player:
                    valid_moves = piece.get_valid_moves(board)
                    for to_x, to_y in valid_moves:
                        moves.append(((x, y), (to_x, to_y)))

        if not moves:
            return -1000 if board.is_in_check('red' if player == 0 else 'black') else 0

        # 移动排序
        moves = self.order_moves(board, moves, player, depth)

        # 第一个节点使用完整窗口
        first_move = True
        best_value = float('-inf')

        for move in moves:
            # 模拟移动
            from_pos, to_pos = move
            piece = board.get_piece(from_pos[0], from_pos[1])
            captured = board.get_piece(to_pos[0], to_pos[1])

            board.grid[to_pos[1]][to_pos[0]] = piece
            board.grid[from_pos[1]][from_pos[0]] = None
            piece.x, piece.y = to_pos

            # 空着裁剪
            if not self.is_futile_move(board, move, player):
                if first_move:
                    value = -self.pvs(board, depth-1, -beta, -alpha, 1-player)
                    first_move = False
                else:
                    # 使用空窗口进行搜索
                    value = -self.pvs(board, depth-1, -
                                      alpha-1, -alpha, 1-player)
                    if value > alpha and value < beta:
                        # 重新搜索
                        value = -self.pvs(board, depth-1, -
                                          beta, -alpha, 1-player)
            else:
                value = alpha  # 空着裁剪

            # 撤销移动
            board.grid[from_pos[1]][from_pos[0]] = piece
            board.grid[to_pos[1]][to_pos[0]] = captured
            piece.x, piece.y = from_pos

            if value > best_value:
                best_value = value
                if value > alpha:
                    alpha = value
                    # 更新历史启发表
                    self.history_table[move] = self.history_table.get(
                        move, 0) + depth * depth

                    if value >= beta:
                        # 更新杀手着法表
                        if depth not in self.killer_moves:
                            self.killer_moves[depth] = []
                        if move not in self.killer_moves[depth]:
                            self.killer_moves[depth].append(move)

                        # Beta截断
                        break

        # 存储到置换表
        if best_value <= alpha_orig:
            flag = 'upper'
        elif best_value >= beta:
            flag = 'lower'
        else:
            flag = 'exact'

        self.transposition_table[board_hash] = (depth, best_value, flag)

        return best_value

    def quiescence_search(self, board, alpha, beta, player):
        """静态搜索"""
        # 检查时间
        self.nodes_searched += 1
        if self.nodes_searched % self.time_check_frequency == 0 and self.is_time_up():
            raise TimeoutError()

        # 获取当前局面评估值
        stand_pat = self.evaluation.evaluate(board, player)

        if stand_pat >= beta:
            return beta
        if stand_pat > alpha:
            alpha = stand_pat

        # 生成所有吃子着法
        captures = []
        for y in range(10):
            for x in range(9):
                piece = board.get_piece(x, y)
                if piece and piece.player == player:
                    valid_moves = piece.get_valid_moves(board)
                    for to_x, to_y in valid_moves:
                        target = board.get_piece(to_x, to_y)
                        if target:  # 只考虑吃子着法
                            captures.append(((x, y), (to_x, to_y)))

        # 按MVV-LVA排序
        captures.sort(key=lambda m: self.get_mvv_lva_score(
            board, m), reverse=True)

        for move in captures:
            # 模拟移动
            from_pos, to_pos = move
            piece = board.get_piece(from_pos[0], from_pos[1])
            captured = board.get_piece(to_pos[0], to_pos[1])

            board.grid[to_pos[1]][to_pos[0]] = piece
            board.grid[from_pos[1]][from_pos[0]] = None
            piece.x, piece.y = to_pos

            value = -self.quiescence_search(board, -beta, -alpha, 1-player)

            # 撤销移动
            board.grid[from_pos[1]][from_pos[0]] = piece
            board.grid[to_pos[1]][to_pos[0]] = captured
            piece.x, piece.y = from_pos

            if value >= beta:
                return beta
            if value > alpha:
                alpha = value

        return alpha

    def order_moves(self, board, moves, player, depth):
        """移动排序"""
        move_scores = []

        for move in moves:
            score = 0

            # 置换表着法
            board_hash = self.get_board_hash(board)
            if board_hash in self.transposition_table:
                stored_depth, _, _ = self.transposition_table[board_hash]
                if stored_depth >= depth:
                    score += 10000

            # 杀手着法
            if depth in self.killer_moves and move in self.killer_moves[depth]:
                score += 9000

            # 历史启发
            score += self.history_table.get(move, 0)

            # MVV-LVA得分
            score += self.get_mvv_lva_score(board, move)

            move_scores.append((move, score))

        # 根据分数排序
        move_scores.sort(key=lambda x: x[1], reverse=True)
        return [move for move, _ in move_scores]

    def get_mvv_lva_score(self, board, move):
        """计算MVV-LVA（最有价值的被吃者-最无价值的攻击者）得分"""
        from_pos, to_pos = move
        attacker = board.get_piece(from_pos[0], from_pos[1])
        victim = board.get_piece(to_pos[0], to_pos[1])

        if not victim:
            return 0

        # 棋子价值
        piece_values = {
            'general': 6,
            'advisor': 2,
            'elephant': 2,
            'horse': 4,
            'chariot': 5,
            'cannon': 3,
            'soldier': 1
        }

        return piece_values.get(victim.piece_type, 0) * 10 - piece_values.get(attacker.piece_type, 0)

    def is_futile_move(self, board, move, player):
        """检查是否是无效移动（用于空着裁剪）"""
        from_pos, to_pos = move
        piece = board.get_piece(from_pos[0], from_pos[1])
        target = board.get_piece(to_pos[0], to_pos[1])

        # 如果是吃子着法，不是无效移动
        if target:
            return False

        # 如果是兵/卒过河，不是无效移动
        if piece.piece_type == 'soldier' and not board.is_across_river(from_pos[1], player):
            return False

        # 如果对方在将军，不是无效移动
        if board.is_in_check('red' if player == 1 else 'black'):
            return False

        return True

    def get_board_hash(self, board):
        """获取棋盘哈希值"""
        return board.to_fen()

    def clear_tables(self):
        """清理表项"""
        self.transposition_table.clear()
        self.killer_moves.clear()
        self.history_table.clear()
        self.cache.clear()

    def is_time_up(self):
        """检查是否超时"""
        return time.time() - self.start_time > self.max_time * self.time_safety_margin
