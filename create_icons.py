import pygame
import os

def create_icons():
    # 初始化pygame
    pygame.init()
    
    # 图标尺寸
    icon_size = 32
    
    # 图标名称列表
    icon_names = [
        "new", "open", "save", 
        "computer_red", "computer_black", 
        "export_image", "flip_board", "view_notation",
        "first_move", "prev_move", "next_move", "last_move",
        "edit_position", "stop_thinking", "settings"
    ]
    
    # 确保图标目录存在
    icon_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "assets", "icons")
    if not os.path.exists(icon_dir):
        os.makedirs(icon_dir)
    
    # 为每个图标创建一个简单的图像
    for icon_name in icon_names:
        # 创建透明表面
        surface = pygame.Surface((icon_size, icon_size), pygame.SRCALPHA)
        
        # 填充背景
        pygame.draw.rect(surface, (200, 200, 200, 180), (0, 0, icon_size, icon_size))
        
        # 绘制边框
        pygame.draw.rect(surface, (100, 100, 100, 255), (0, 0, icon_size, icon_size), 1)
        
        # 添加文字
        try:
            font = pygame.font.SysFont(None, 12)
            text = font.render(icon_name, True, (0, 0, 0))
            text_rect = text.get_rect(center=(icon_size//2, icon_size//2))
            surface.blit(text, text_rect)
        except Exception as e:
            print(f"添加文字失败: {e}")
        
        # 保存图标
        try:
            icon_path = os.path.join(icon_dir, f"{icon_name}.png")
            pygame.image.save(surface, icon_path)
            print(f"已创建图标: {icon_path}")
        except Exception as e:
            print(f"保存图标失败 {icon_name}: {e}")
    
    print("图标创建完成")
    pygame.quit()

if __name__ == "__main__":
    create_icons()
