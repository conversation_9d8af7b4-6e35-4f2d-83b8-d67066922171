from evaluation import Evaluation
from search import Search


class AIEngine:
    """AI引擎类，负责决策最佳走法"""

    def __init__(self, difficulty='normal'):
        self.evaluation = Evaluation()
        self.search = Search(self.evaluation)
        self.difficulty = difficulty
        self.max_depth = 8  # 增加最大搜索深度
        self.max_time = 7   # 适当增加最大搜索时间（秒）

        # 根据难度调整搜索参数
        if difficulty == 'easy':
            self.max_depth = 4
            self.max_time = 2
        elif difficulty == 'normal':
            self.max_depth = 6
            self.max_time = 5
        elif difficulty == 'hard':
            self.max_depth = 8
            self.max_time = 7
        elif difficulty == 'expert':
            self.max_depth = 10
            self.max_time = 10

    def get_move(self, board, player):
        """获取AI的最佳走法"""
        # 动态调整搜索深度
        current_depth = self.get_dynamic_depth(board)

        # 获取所有可能的移动
        moves = []
        for y in range(10):
            for x in range(9):
                piece = board.get_piece(x, y)
                if piece and piece.player == player:
                    valid_moves = piece.get_valid_moves(board)
                    for to_x, to_y in valid_moves:
                        moves.append(((x, y), (to_x, to_y)))

        if not moves:
            return None

        # 使用搜索算法找到最佳移动
        best_move = self.search.find_best_move(
            board,
            moves,
            player,
            current_depth,
            self.max_time
        )

        return best_move

    def get_dynamic_depth(self, board):
        """根据局面复杂度动态调整搜索深度"""
        piece_count = 0
        for y in range(10):
            for x in range(9):
                if board.get_piece(x, y):
                    piece_count += 1

        # 根据棋子数量动态调整深度
        if piece_count <= 10:  # 残局
            return min(self.max_depth + 2, 12)  # 增加搜索深度
        elif piece_count <= 20:  # 中局
            return self.max_depth
        else:  # 开局
            return max(self.max_depth - 1, 4)  # 稍微降低深度

    def evaluate_position(self, board, player):
        """评估当前局面"""
        return self.evaluation.evaluate(board, player)
