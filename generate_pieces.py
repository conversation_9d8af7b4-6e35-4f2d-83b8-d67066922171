#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PIL import Image, ImageDraw, ImageFont
import os
import sys

def create_piece_image(text, size=(73, 73), bg_color=(0, 0, 0), text_color=(255, 255, 255), border_color=(255, 255, 255), save_path=None):
    """创建简单的中国象棋棋子图片"""
    try:
        # 创建一个新的图像，背景为指定颜色
        img = Image.new('RGB', size, bg_color)
        draw = ImageDraw.Draw(img)
        
        # 绘制圆形边框
        draw.ellipse([(2, 2), (size[0]-3, size[1]-3)], outline=border_color, width=2)
        
        # 尝试使用不同的字体渲染文字
        try:
            # 尝试查找系统上的字体
            font_found = False
            font = None
            
            # 尝试常见的中文字体路径
            font_paths = [
                "C:/Windows/Fonts/simhei.ttf",
                "C:/Windows/Fonts/simsun.ttc",
                "C:/Windows/Fonts/msyh.ttc"
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        font = ImageFont.truetype(font_path, 36)
                        font_found = True
                        print(f"使用字体: {font_path}")
                        break
                    except Exception as e:
                        print(f"尝试加载字体 {font_path} 失败: {e}")
                        continue
            
            # 如果找不到任何可用字体，使用默认字体
            if not font_found:
                print("未找到中文字体，使用默认字体")
                font = ImageFont.load_default()
            
            # 计算文本位置（使文本居中）
            # 在PIL 9.0+中，使用getbbox来获取文本尺寸
            if hasattr(font, 'getbbox'):
                bbox = font.getbbox(text)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
            else:
                # 估计文本尺寸
                text_width = 20
                text_height = 36
            
            # 计算文本的居中位置
            x = (size[0] - text_width) // 2
            y = (size[1] - text_height) // 2
            
            # 绘制文本
            draw.text((x, y), text, font=font, fill=text_color)
            
        except Exception as e:
            print(f"渲染文字失败: {e}")
            # 失败时使用简单的文本代替
            draw.text((size[0]//3, size[1]//3), text, fill=text_color)
        
        # 保存图像
        if save_path:
            directory = os.path.dirname(save_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
            img.save(save_path)
            print(f"保存图片: {save_path}")
        
        return img
    
    except Exception as e:
        print(f"创建棋子图片失败: {e}")
        return None

def generate_chess_pieces():
    """生成所有中国象棋棋子图片"""
    print("开始生成中国象棋棋子图片...")
    
    # 确保输出目录存在
    output_dir = "assets/images/pieces"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")
    
    # 定义棋子
    red_pieces = {
        "general": "帅",
        "advisor": "士", 
        "elephant": "相",
        "horse": "马",
        "chariot": "车",
        "cannon": "炮",
        "soldier": "兵"
    }
    
    black_pieces = {
        "general": "将",
        "advisor": "仕",
        "elephant": "象",
        "horse": "马",
        "chariot": "车", 
        "cannon": "炮",
        "soldier": "卒"
    }
    
    # 生成红方棋子（红底白字）
    for piece_type, text in red_pieces.items():
        filename = f"{output_dir}/red_{piece_type}.png"
        create_piece_image(
            text=text,
            bg_color=(180, 0, 0),  # 深红色背景
            text_color=(255, 255, 255),  # 白色文字
            border_color=(255, 255, 255),  # 白色边框
            save_path=filename
        )
    
    # 生成黑方棋子（黑底白字）
    for piece_type, text in black_pieces.items():
        filename = f"{output_dir}/black_{piece_type}.png"
        create_piece_image(
            text=text, 
            bg_color=(0, 0, 0),  # 黑色背景
            text_color=(255, 255, 255),  # 白色文字
            border_color=(255, 255, 255),  # 白色边框
            save_path=filename
        )
    
    print("所有棋子图片生成完毕！")

if __name__ == "__main__":
    generate_chess_pieces()
