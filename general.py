from piece import Piece

class General(Piece):
    def __init__(self, x, y, player):
        super().__init__(x, y, player, "general")
    
    def is_valid_move(self, to_x, to_y, board):
        # 基本检查
        target_piece = board.get_piece(to_x, to_y)
        if target_piece and target_piece.player == self.player:
            return False
        
        # 将/帅只能在九宫格内移动
        if self.player == 0:  # 红方
            if not (3 <= to_x <= 5 and 0 <= to_y <= 2):
                return False
        else:  # 黑方
            if not (3 <= to_x <= 5 and 7 <= to_y <= 9):
                return False
        
        # 将/帅只能横向或纵向移动一格
        dx = abs(to_x - self.x)
        dy = abs(to_y - self.y)
        if (dx == 1 and dy == 0) or (dx == 0 and dy == 1):
            return True
        
        # 特殊规则：将帅对脸
        if dx == 0 and self.x == to_x:
            # 检查是否在同一列
            min_y = min(self.y, to_y)
            max_y = max(self.y, to_y)
            
            # 检查中间是否有其他棋子
            has_piece_between = False
            for y in range(min_y + 1, max_y):
                if board.get_piece(self.x, y):
                    has_piece_between = True
                    break
            
            # 如果没有棋子阻挡，且目标是对方的将/帅，则可以"将帅对脸"
            if not has_piece_between:
                target_piece = board.get_piece(to_x, to_y)
                if target_piece and target_piece.piece_type == "general" and target_piece.player != self.player:
                    return True
        
        return False