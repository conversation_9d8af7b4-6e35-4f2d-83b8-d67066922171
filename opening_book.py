import os
import json
import random

class OpeningBook:
    def __init__(self):
        self.openings = {}
        self.load_openings()
    
    def load_openings(self):
        """加载开局库数据"""
        # 开局库文件路径
        openings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "assets", "openings.json")
        
        # 检查文件是否存在
        if os.path.exists(openings_path):
            try:
                with open(openings_path, 'r', encoding='utf-8') as f:
                    self.openings = json.load(f)
                print("成功加载开局库，包含 %d 个开局变例" % len(self.openings))
            except Exception as e:
                print("加载开局库失败: %s" % e)
                self.create_default_openings(openings_path)
        else:
            print("开局库文件不存在: %s" % openings_path)
            self.create_default_openings(openings_path)
    
    def create_default_openings(self, save_path):
        """创建默认开局库"""
        # 常见中国象棋开局
        default_openings = {
            # 起始局面FEN字符串
            "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C5C1/9/RNBAKABNR": {
                # 可能的走法: [从坐标, 到坐标, 描述, 权重]
                "moves": [
                    [[2, 9], [2, 7], "起马炮", 80],  # 红方起马
                    [[7, 9], [7, 7], "起马炮", 80],  # 红方起马
                    [[1, 9], [2, 7], "起士", 40],    # 红方起士
                    [[8, 9], [7, 7], "起士", 40],    # 红方起士
                    [[4, 9], [5, 8], "动帅", 20]     # 红方动帅
                ]
            },
            # 当红方起马后的局面
            "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1CN4C1/9/R1BAKABNR": {
                "moves": [
                    [[2, 0], [2, 2], "黑方起马", 70],
                    [[7, 0], [7, 2], "黑方起马", 70],
                    [[1, 0], [2, 2], "黑方起士", 30],
                    [[8, 0], [7, 2], "黑方起士", 30]
                ]
            },
            # 其他常见开局变例...
            "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/1C4NC1/9/R1BAKAB1R": {
                "moves": [
                    [[2, 0], [2, 2], "当头炮", 60],
                    [[1, 0], [2, 2], "进士", 40]
                ]
            },
            # 屏风马对卒底炮
            "rnbakabnr/9/1c5c1/p1p1p1p1p/9/9/P1P1P1P1P/4C2C1/9/RN1AKABNR": {
                "moves": [
                    [[7, 9], [6, 7], "飞相", 70],
                    [[0, 9], [2, 7], "起马", 80],
                    [[2, 9], [1, 7], "进炮", 40]
                ]
            },
            # 中炮对屏风马
            "rn1akab1r/9/1c2b3c/p1p1p1p1p/9/9/P1P1P1P1P/1C2C4/9/RN1AKABNR": {
                "moves": [
                    [[7, 9], [6, 7], "进相", 50],
                    [[0, 9], [2, 7], "起马", 60],
                    [[4, 9], [5, 8], "动帅", 30]
                ]
            }
        }
        
        self.openings = default_openings
        
        # 保存开局库
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(default_openings, f, ensure_ascii=False, indent=2)
            print("创建并保存了默认开局库: %s" % save_path)
        except Exception as e:
            print("保存默认开局库失败: %s" % e)
    
    def get_move_from_position(self, fen):
        """根据当前局面FEN获取开局库中的走法"""
        if fen in self.openings:
            moves = self.openings[fen]["moves"]
            
            # 根据权重随机选择一个走法
            weights = [move[3] for move in moves]
            total_weight = sum(weights)
            
            # 如果没有有效权重，则返回None
            if total_weight == 0:
                return None
                
            # 随机数在0到总权重之间
            r = random.randint(1, total_weight)
            
            # 根据权重选择走法
            weight_sum = 0
            for move in moves:
                weight_sum += move[3]
                if r <= weight_sum:
                    return move[:2]  # 返回[from_pos, to_pos]
        
        return None
    
    def add_opening_move(self, fen, from_pos, to_pos, description="", weight=50):
        """添加新的开局走法到开局库"""
        if fen not in self.openings:
            self.openings[fen] = {"moves": []}
        
        # 检查是否已经存在相同的走法
        for i, move in enumerate(self.openings[fen]["moves"]):
            if move[0] == from_pos and move[1] == to_pos:
                # 更新描述和权重
                self.openings[fen]["moves"][i][2] = description
                self.openings[fen]["moves"][i][3] = weight
                return
        
        # 添加新走法
        self.openings[fen]["moves"].append([from_pos, to_pos, description, weight])
        
        # 保存更新后的开局库
        self.save_openings()
    
    def save_openings(self):
        """保存开局库到文件"""
        openings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "assets", "openings.json")
        
        try:
            with open(openings_path, 'w', encoding='utf-8') as f:
                json.dump(self.openings, f, ensure_ascii=False, indent=2)
            print("成功保存开局库")
        except Exception as e:
            print("保存开局库失败: %s" % e)
    
    def get_opening_description(self, fen, from_pos, to_pos):
        """获取开局走法的描述"""
        if fen in self.openings:
            for move in self.openings[fen]["moves"]:
                if move[0] == from_pos and move[1] == to_pos:
                    return move[2]
        return ""
