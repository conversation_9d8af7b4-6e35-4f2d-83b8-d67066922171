import os
import sys
import pygame
import traceback

def main():
    """简单的测试函数，用于验证基本功能"""
    try:
        print("测试pygame初始化...")
        pygame.init()
        print("pygame初始化成功")
        
        print("测试创建窗口...")
        screen = pygame.display.set_mode((800, 600))
        pygame.display.set_caption("测试")
        print("创建窗口成功")
        
        # 测试声音
        try:
            print("测试声音系统...")
            pygame.mixer.init()
            print("声音系统初始化成功")
        except Exception as e:
            print(f"声音系统初始化失败: {e}")
        
        # 简单绘制一些内容
        screen.fill((240, 240, 240))
        pygame.draw.rect(screen, (200, 0, 0), pygame.Rect(100, 100, 200, 200))
        pygame.display.flip()
        
        # 简单的事件循环
        print("进入测试事件循环，按ESC退出...")
        running = True
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
            
            pygame.time.delay(100)  # 100ms延迟减少CPU占用
        
    except Exception as e:
        print(f"测试失败: {e}")
        traceback.print_exc()
    finally:
        pygame.quit()
        print("测试结束")

if __name__ == "__main__":
    main()
