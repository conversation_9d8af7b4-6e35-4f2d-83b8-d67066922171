import pygame
import os
from constants import PIECE_RADIUS, RED_PLAYER, BLACK_PLAYER
from rules import Rules


class Piece:
    def __init__(self, x, y, player, piece_type):
        self.x = x
        self.y = y
        self.player = player  # 0 for red, 1 for black
        self.piece_type = piece_type
        self.image = None
        self.load_image()

    def load_image(self):
        # 加载棋子图片
        color = "red" if self.player == RED_PLAYER else "black"
        image_path = os.path.join(
            "assets", "images", f"{color}_{self.piece_type}.png")

        try:
            original_image = pygame.image.load(image_path)
            # 不进行缩放，保持原始尺寸
            self.image = original_image
        except:
            # 如果图片加载失败，创建一个简单的棋子图形
            self.image = pygame.Surface(
                (PIECE_RADIUS * 2, PIECE_RADIUS * 2), pygame.SRCALPHA)
            pygame.draw.circle(self.image, (255, 0, 0) if self.player == RED_PLAYER else (
                0, 0, 0), (PIECE_RADIUS, PIECE_RADIUS), PIECE_RADIUS)
            font = pygame.font.SysFont(None, 30)
            text = font.render(
                self.piece_type[0].upper(), True, (255, 255, 255))
            text_rect = text.get_rect(center=(PIECE_RADIUS, PIECE_RADIUS))
            self.image.blit(text, text_rect)

    def is_valid_move(self, to_x, to_y, board):
        # 基本检查：目标位置不能有自己的棋子
        target_piece = board.get_piece(to_x, to_y)
        if target_piece and target_piece.player == self.player:
            return False

        # 根据棋子类型实现不同的移动规则
        if self.piece_type == "chariot":  # 车
            return self._is_valid_chariot_move(to_x, to_y, board)
        elif self.piece_type == "horse":  # 马
            return self._is_valid_horse_move(to_x, to_y, board)
        elif self.piece_type == "elephant":  # 象/相
            return self._is_valid_elephant_move(to_x, to_y, board)
        elif self.piece_type == "advisor":  # 士/仕
            return self._is_valid_advisor_move(to_x, to_y, board)
        elif self.piece_type == "cannon":  # 炮
            return self._is_valid_cannon_move(to_x, to_y, board)
        elif self.piece_type == "soldier":  # 兵/卒
            return self._is_valid_soldier_move(to_x, to_y, board)

        return False

    def _is_valid_chariot_move(self, to_x, to_y, board):
        # 车只能直线移动
        if self.x != to_x and self.y != to_y:
            return False

        # 检查路径上是否有其他棋子
        pieces_between = Rules.get_pieces_between(
            self.x, self.y, to_x, to_y, board)
        return len(pieces_between) == 0

    def _is_valid_horse_move(self, to_x, to_y, board):
        # 马走"日"字
        dx = abs(to_x - self.x)
        dy = abs(to_y - self.y)
        if not ((dx == 1 and dy == 2) or (dx == 2 and dy == 1)):
            return False

        # 使用Rules类中的方法检查"蹩马腿"
        if Rules.is_horse_blocked(self.x, self.y, to_x, to_y, board):
            return False

        return True

    def _is_valid_elephant_move(self, to_x, to_y, board):
        # 象/相走"田"字
        dx = abs(to_x - self.x)
        dy = abs(to_y - self.y)
        if dx != 2 or dy != 2:
            return False

        # 检查象眼
        block_x = (self.x + to_x) // 2
        block_y = (self.y + to_y) // 2
        if board.get_piece(block_x, block_y):
            return False

        # 象/相不能过河
        if self.player == RED_PLAYER and to_y > 4:
            return False
        if self.player == BLACK_PLAYER and to_y < 5:
            return False

        return True

    def _is_valid_advisor_move(self, to_x, to_y, board):
        # 士/仕走斜线
        dx = abs(to_x - self.x)
        dy = abs(to_y - self.y)
        if dx != 1 or dy != 1:
            return False

        # 士/仕只能在九宫格内移动
        if not Rules.is_in_palace(to_x, to_y, self.player):
            return False

        return True

    def _is_valid_cannon_move(self, to_x, to_y, board):
        # 炮移动规则：直线移动，吃子时必须翻过一个棋子
        if self.x != to_x and self.y != to_y:
            return False

        # 使用Rules类中的方法检查炮的跳跃是否有效
        return Rules.is_cannon_jump_valid(self.x, self.y, to_x, to_y, board)

    def _is_valid_soldier_move(self, to_x, to_y, board):
        # 兵/卒只能向前移动一格，过河后可以向左右或向前移动一格
        dx = to_x - self.x
        dy = to_y - self.y

        # 检查是否过河
        crossed_river = Rules.is_across_river(self.y, self.player)

        # 每次移动只能走一格
        if abs(dx) > 1 or abs(dy) > 1 or (abs(dx) == 1 and abs(dy) == 1):
            return False

        # 兵/卒不能后退
        if self.player == RED_PLAYER:
            # 红方兵(现在在上方)向下移动
            if dy < 0:  # 不能后退
                return False

            if crossed_river:
                # 过河后可以向前或向左右移动一格
                if (dx == 0 and dy == 1) or (abs(dx) == 1 and dy == 0):
                    return True
                return False
            else:
                # 未过河只能向前移动一格
                return dx == 0 and dy == 1
        else:
            # 黑方卒(现在在下方)向上移动
            if dy > 0:  # 不能后退
                return False

            if crossed_river:
                # 过河后可以向前或向左右移动一格
                if (dx == 0 and dy == -1) or (abs(dx) == 1 and dy == 0):
                    return True
                return False
            else:
                # 未过河只能向前移动一格
                return dx == 0 and dy == -1

    def get_valid_moves(self, board):
        """获取所有可能的合法移动位置"""
        valid_moves = []
        # 遍历棋盘所有位置检查是否是合法移动
        for y in range(10):
            for x in range(9):
                if self.is_valid_move(x, y, board):
                    valid_moves.append((x, y))
        return valid_moves

    def render(self, screen, pos=None):
        # 渲染棋子
        if pos is None:
            from constants import BOARD_OFFSET_X, BOARD_OFFSET_Y, GRID_WIDTH, GRID_HEIGHT
            # 使用棋子在棋盘上的位置计算屏幕坐标
            screen_x = BOARD_OFFSET_X + self.x * GRID_WIDTH + \
                GRID_WIDTH // 2 - self.image.get_width() // 2
            screen_y = BOARD_OFFSET_Y + self.y * GRID_HEIGHT + \
                GRID_HEIGHT // 2 - self.image.get_height() // 2
            screen.blit(self.image, (screen_x, screen_y))
        else:
            # 使用提供的位置
            x, y = pos
            screen.blit(self.image, (x - self.image.get_width() //
                        2, y - self.image.get_height() // 2))

    def get_image(self):
        """获取棋子图像"""
        return self.image
