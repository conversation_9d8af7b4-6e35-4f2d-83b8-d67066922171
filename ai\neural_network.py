import numpy as np
import os
import time

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("PyTorch 未安装，将使用基本评估函数")

# 棋盘状态编码常量
PIECE_ENCODING = {
    "general_0": 1,   # 红方将
    "advisor_0": 2,   # 红方士
    "elephant_0": 3,  # 红方象
    "horse_0": 4,     # 红方马
    "chariot_0": 5,   # 红方车
    "cannon_0": 6,    # 红方炮
    "soldier_0": 7,   # 红方兵
    "general_1": -1,  # 黑方将
    "advisor_1": -2,  # 黑方士
    "elephant_1": -3, # 黑方象
    "horse_1": -4,    # 黑方马
    "chariot_1": -5,  # 黑方车
    "cannon_1": -6,   # 黑方炮
    "soldier_1": -7,  # 黑方兵
}

class ChessNet(nn.Module):
    """中国象棋神经网络模型"""
    def __init__(self):
        super(ChessNet, self).__init__()
        
        # 输入: 9x10x15 (9x10棋盘，每个位置15个可能的状态)
        # 15个状态: 空位, 红方7种棋子, 黑方7种棋子
        self.conv1 = nn.Conv2d(15, 64, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(64)
        self.conv2 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm2d(128)
        self.conv3 = nn.Conv2d(128, 256, kernel_size=3, padding=1)
        self.bn3 = nn.BatchNorm2d(256)
        
        # 全连接层
        self.fc1 = nn.Linear(256 * 9 * 10, 512)
        self.dropout1 = nn.Dropout(0.3)
        self.fc2 = nn.Linear(512, 256)
        self.dropout2 = nn.Dropout(0.3)
        self.fc3 = nn.Linear(256, 1)  # 输出一个值，表示局面评分
        
    def forward(self, x):
        # 卷积层
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        
        # 展平
        x = x.view(x.size(0), -1)
        
        # 全连接层
        x = F.relu(self.fc1(x))
        x = self.dropout1(x)
        x = F.relu(self.fc2(x))
        x = self.dropout2(x)
        x = self.fc3(x)
        
        # 使用tanh激活函数，将输出限制在[-1, 1]范围内，然后缩放到合适的评分范围
        x = torch.tanh(x) * 1000  # 缩放到[-1000, 1000]
        
        return x

class NeuralEvaluator:
    """使用神经网络的棋盘评估器"""
    def __init__(self, model_path=None):
        self.model = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.batch_size = 64  # 批量评估的大小
        self.evaluation_cache = {}  # 评估缓存
        
        if TORCH_AVAILABLE:
            # 创建模型
            self.model = ChessNet().to(self.device)
            
            # 加载预训练模型（如果存在）
            if model_path and os.path.exists(model_path):
                try:
                    self.model.load_state_dict(torch.load(model_path, map_location=self.device))
                    self.model.eval()  # 设置为评估模式
                    print(f"成功加载模型: {model_path}")
                except Exception as e:
                    print(f"加载模型失败: {e}")
                    self.model = None
            
            # 如果没有预训练模型，创建模型目录
            elif model_path:
                model_dir = os.path.dirname(model_path)
                if not os.path.exists(model_dir):
                    os.makedirs(model_dir)
                    print(f"创建模型目录: {model_dir}")
        
    def board_to_tensor(self, board, player):
        """将棋盘转换为神经网络输入张量"""
        # 创建15通道的张量 (空位 + 红方7种棋子 + 黑方7种棋子)
        tensor = torch.zeros(15, 10, 9, device=self.device)
        
        # 遍历棋盘
        for y in range(10):
            for x in range(9):
                piece = board.get_piece(x, y)
                if piece:
                    # 获取棋子类型和所属方
                    piece_type = piece.piece_type
                    piece_player = piece.player
                    
                    # 计算通道索引 (1-7为红方棋子，8-14为黑方棋子)
                    key = f"{piece_type}_{piece_player}"
                    encoding = PIECE_ENCODING.get(key, 0)
                    
                    if encoding > 0:  # 红方棋子
                        tensor[encoding, y, x] = 1
                    elif encoding < 0:  # 黑方棋子
                        tensor[abs(encoding) + 7, y, x] = 1
                else:
                    # 空位
                    tensor[0, y, x] = 1
        
        # 如果当前玩家是黑方，翻转棋盘
        if player == 1:
            tensor = torch.flip(tensor, [1])  # 沿着y轴翻转
        
        return tensor.unsqueeze(0)  # 添加批次维度
    
    def get_board_hash(self, board):
        """获取棋盘的哈希值，用于缓存"""
        board_state = board.get_board_state()
        return hash(str(board_state))
    
    def evaluate(self, board, player):
        """评估棋盘局面"""
        if not TORCH_AVAILABLE or self.model is None:
            # 如果PyTorch不可用或模型未加载，使用传统评估
            return self.traditional_evaluate(board, player)
        
        # 检查缓存
        board_hash = self.get_board_hash(board)
        if board_hash in self.evaluation_cache:
            return self.evaluation_cache[board_hash]
        
        # 将棋盘转换为张量
        tensor = self.board_to_tensor(board, player)
        
        # 使用模型评估
        with torch.no_grad():
            score = self.model(tensor).item()
        
        # 缓存结果
        self.evaluation_cache[board_hash] = score
        
        return score
    
    def batch_evaluate(self, boards, players):
        """批量评估多个棋盘局面"""
        if not TORCH_AVAILABLE or self.model is None:
            # 如果PyTorch不可用或模型未加载，使用传统评估
            return [self.traditional_evaluate(board, player) for board, player in zip(boards, players)]
        
        # 创建批量输入张量
        tensors = []
        board_hashes = []
        uncached_indices = []
        results = [0] * len(boards)
        
        # 检查缓存并收集未缓存的棋盘
        for i, (board, player) in enumerate(zip(boards, players)):
            board_hash = self.get_board_hash(board)
            board_hashes.append(board_hash)
            
            if board_hash in self.evaluation_cache:
                results[i] = self.evaluation_cache[board_hash]
            else:
                tensors.append(self.board_to_tensor(board, player))
                uncached_indices.append(i)
        
        if tensors:
            # 合并张量
            batch_tensor = torch.cat(tensors, dim=0)
            
            # 批量评估
            with torch.no_grad():
                scores = self.model(batch_tensor).cpu().numpy().flatten()
            
            # 更新结果和缓存
            for idx, score in zip(uncached_indices, scores):
                results[idx] = float(score)
                self.evaluation_cache[board_hashes[idx]] = float(score)
        
        return results
    
    def traditional_evaluate(self, board, player):
        """传统的棋盘评估方法（作为备选）"""
        score = 0
        
        # 棋子基础价值
        piece_values = {
            "general": 10000,  # 将/帅
            "advisor": 200,    # 士/仕
            "elephant": 200,   # 象/相
            "horse": 400,      # 马
            "chariot": 900,    # 车
            "cannon": 450,     # 炮
            "soldier": 100     # 兵/卒
        }
        
        # 计算双方棋子价值
        for y in range(10):
            for x in range(9):
                piece = board.get_piece(x, y)
                if piece:
                    piece_value = piece_values.get(piece.piece_type, 0)
                    
                    if piece.player == player:
                        score += piece_value
                    else:
                        score -= piece_value
        
        return score
    
    def train(self, dataset, epochs=10, batch_size=64, learning_rate=0.001):
        """训练神经网络模型"""
        if not TORCH_AVAILABLE or self.model is None:
            print("PyTorch不可用或模型未初始化，无法训练")
            return False
        
        # 设置为训练模式
        self.model.train()
        
        # 定义优化器和损失函数
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)
        criterion = nn.MSELoss()
        
        # 训练循环
        for epoch in range(epochs):
            total_loss = 0
            batch_count = 0
            
            # 打乱数据集
            indices = np.random.permutation(len(dataset))
            
            # 按批次处理
            for i in range(0, len(dataset), batch_size):
                batch_indices = indices[i:i+batch_size]
                batch_inputs = []
                batch_targets = []
                
                for idx in batch_indices:
                    board_tensor, score = dataset[idx]
                    batch_inputs.append(board_tensor)
                    batch_targets.append(score)
                
                # 合并批次数据
                inputs = torch.cat(batch_inputs, dim=0).to(self.device)
                targets = torch.tensor(batch_targets, dtype=torch.float32).view(-1, 1).to(self.device)
                
                # 前向传播
                outputs = self.model(inputs)
                loss = criterion(outputs, targets)
                
                # 反向传播和优化
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
                batch_count += 1
            
            # 打印每个epoch的平均损失
            avg_loss = total_loss / batch_count
            print(f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}")
        
        # 训练后设置为评估模式
        self.model.eval()
        return True
    
    def save_model(self, path):
        """保存模型"""
        if not TORCH_AVAILABLE or self.model is None:
            print("PyTorch不可用或模型未初始化，无法保存")
            return False
        
        try:
            torch.save(self.model.state_dict(), path)
            print(f"模型已保存到: {path}")
            return True
        except Exception as e:
            print(f"保存模型失败: {e}")
            return False