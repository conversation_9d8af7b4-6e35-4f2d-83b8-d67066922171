import traceback

try:
    print("开始导入模块...")
    import pygame
    print("成功导入 pygame")
    import sys
    import os
    from game import Game
    print("成功导入 Game 类")
    from constants import WINDOW_WIDTH, WINDOW_HEIGHT, TITLE, TOOLBAR_HEIGHT
    print("成功导入常量")
    
    print("初始化 pygame...")
    pygame.init()
    print("设置游戏窗口...")
    pygame.display.set_caption(TITLE)
    
    print("创建游戏窗口...")
    screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT + TOOLBAR_HEIGHT))
    
    print("创建 Game 实例...")
    game = Game(screen)
    
    print("游戏实例创建成功，退出调试")
    pygame.quit()
    sys.exit()

except Exception as e:
    print(f"错误: {e}")
    print("详细错误信息:")
    traceback.print_exc()
