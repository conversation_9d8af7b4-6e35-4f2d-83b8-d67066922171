import os
import time
import math
import wave
import struct

def create_sound_file(filename, frequency=440, duration=0.3, volume=0.5, wave_type='sine'):
    """
    创建简单的音效文件
    
    参数:
    - filename: 保存的文件名
    - frequency: 音调频率（赫兹），默认是440Hz（标准A音）
    - duration: 持续时间（秒）
    - volume: 音量（0.0到1.0之间）
    - wave_type: 波形类型，'sine'为正弦波, 'square'为方波
    """
    # 采样率和采样点数
    sample_rate = 44100
    n_samples = int(sample_rate * duration)
    
    # 打开WAV文件进行写入
    with wave.open(filename, 'w') as wav_file:
        # 设置WAV文件参数：通道数1，2字节/样本，采样率，采样点数
        wav_file.setparams((1, 2, sample_rate, n_samples, 'NONE', 'not compressed'))
        
        # 生成音频数据
        for i in range(n_samples):
            if wave_type == 'sine':
                # 正弦波 - 温和的声音
                value = int(volume * 32767 * math.sin(2 * math.pi * frequency * i / sample_rate))
            elif wave_type == 'square':
                # 方波 - 锐利的声音
                value = int(volume * 32767) if (i % int(sample_rate / frequency) < int(sample_rate / frequency / 2)) else -int(volume * 32767)
            
            # 将16位整数打包为二进制数据
            packed_value = struct.pack('h', value)
            wav_file.writeframes(packed_value)
    
    print(f"已创建音效文件: {filename}")

def recreate_sound_files():
    """重新创建所有需要的音效文件"""
    # 音效文件目录
    sound_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "assets", "sounds")
    
    # 确保音效目录存在
    if not os.path.exists(sound_dir):
        os.makedirs(sound_dir)
        print(f"创建音效目录: {sound_dir}")
    
    # 创建各种音效
    sounds = {
        # 文件名: (频率, 持续时间, 音量, 波形类型)
        "select.wav": (660, 0.1, 0.3, 'sine'),  # 高音短促 - 选择
        "move.wav": (440, 0.2, 0.4, 'sine'),    # 中音 - 移动
        "capture.wav": (330, 0.3, 0.6, 'square'), # 低沉方波 - 吃子
        "check.wav": (880, 0.3, 0.5, 'square'),  # 高音方波 - 将军
        "checkmate.wav": (220, 0.6, 0.7, 'square'), # 低音长 - 将死
        "button.wav": (550, 0.1, 0.3, 'sine'),  # 中高音短促 - 按钮
        "illegal.wav": (110, 0.2, 0.5, 'square'), # 很低的方波 - 非法走法
        "start.wav": (440, 0.5, 0.4, 'sine'),   # 中音长 - 开始游戏
        "win.wav": (660, 0.5, 0.6, 'sine'),     # 高音长 - 胜利
        "lose.wav": (220, 0.5, 0.5, 'square')   # 低音长方波 - 失败
    }
    
    # 创建每个音效文件
    for filename, params in sounds.items():
        sound_path = os.path.join(sound_dir, filename)
        freq, duration, volume, wave_type = params
        create_sound_file(sound_path, freq, duration, volume, wave_type)
        time.sleep(0.1)  # 短暂暂停，避免可能的资源冲突

if __name__ == "__main__":
    print("开始重新创建音效文件...")
    recreate_sound_files()
    print("所有音效文件创建完成！")
