import sys
import traceback

# 重定向标准输出和错误输出到文件
with open('error_log.txt', 'w', encoding='utf-8') as f:
    try:
        print("开始导入模块...", file=f)
        import pygame
        print("成功导入 pygame", file=f)
        import os
        from game import Game
        print("成功导入 Game 类", file=f)
        from constants import WINDOW_WIDTH, WINDOW_HEIGHT, TITLE, TOOLBAR_HEIGHT
        print("成功导入常量", file=f)
        
        print("初始化 pygame...", file=f)
        pygame.init()
        print("设置游戏窗口...", file=f)
        pygame.display.set_caption(TITLE)
        
        print("创建游戏窗口...", file=f)
        screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT + TOOLBAR_HEIGHT))
        
        print("创建 Game 实例...", file=f)
        game = Game(screen)
        
        print("游戏实例创建成功，退出调试", file=f)
        pygame.quit()
    
    except Exception as e:
        print(f"错误: {e}", file=f)
        print("详细错误信息:", file=f)
        traceback.print_exc(file=f)

print("测试完成，请查看 error_log.txt 文件获取详细信息")
