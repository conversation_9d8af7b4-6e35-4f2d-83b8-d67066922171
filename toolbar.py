import os
import pygame

# 检查Pygame版本
pygame_version = pygame.version.vernum
supports_border_radius = pygame_version >= (2, 0, 0)

class Toolbar:
    def __init__(self, game):
        self.game = game
        self.height = 40
        self.button_width = 40
        self.button_height = self.height - 4
        self.buttons = []
        self.padding = 5
        self.current_x = self.padding
        self.toolbar_font = None
        self.toolbar_background = (230, 230, 230)
        self.toolbar_border = (180, 180, 180)
        self.toolbar_gradient_top = (240, 240, 240)
        self.toolbar_gradient_bottom = (210, 210, 210)
        
        # 用于分组按钮
        self.separator_width = 10
        
        # 加载按钮图标
        self.load_icons()
        
        # 初始不显示tooltip
        self.current_tooltip = None
        self.tooltip_pos = (0, 0)
        self.tooltip_visible = False
        self.tooltip_time = 0
        self.tooltip_delay = 0.8  # 显示tooltip的延迟时间（秒）
    
    def load_icons(self):
        """加载所有按钮图标"""
        self.icons = {}
        icon_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "assets", "icons")
        
        # 确保图标目录存在
        if not os.path.exists(icon_dir):
            os.makedirs(icon_dir)
            print(f"创建图标目录: {icon_dir}")
        
        # 按钮图标列表
        icon_names = [
            "new", "open", "save", 
            "computer_red", "computer_black", 
            "export_image", "flip_board", "view_notation",
            "first_move", "prev_move", "next_move", "last_move",
            "edit_position", "stop_thinking", "settings",
            "separator"
        ]
        
        # 尝试加载图标，如果不存在则使用占位符
        for name in icon_names:
            icon_path = os.path.join(icon_dir, f"{name}.png")
            try:
                if os.path.exists(icon_path):
                    icon = pygame.image.load(icon_path)
                    icon = pygame.transform.scale(icon, (self.button_width - 8, self.button_height - 8))
                    self.icons[name] = icon
                else:
                    # 如果图标不存在，创建一个带文字的占位图标
                    self.icons[name] = self.create_placeholder_icon(name)
            except Exception as e:
                print(f"加载图标 {name} 失败: {e}")
                self.icons[name] = self.create_placeholder_icon(name)
    
    def create_placeholder_icon(self, name):
        """为缺失的图标创建占位符"""
        # 创建一个带有文字的占位符图标
        icon_size = (self.button_width - 8, self.button_height - 8)
        icon_surface = pygame.Surface(icon_size, pygame.SRCALPHA)
        
        # 根据图标名称绘制不同样式的占位符
        if name == "separator":
            # 分隔符图标
            icon_surface.fill((0, 0, 0, 0))  # 透明
            return icon_surface
        
        # 为不同类型的按钮创建不同颜色和样式的图标
        icon_colors = {
            "new": (50, 150, 50),      # 绿色系
            "open": (80, 120, 220),    # 蓝色系
            "save": (80, 120, 220),    # 蓝色系
            "computer_red": (220, 80, 80),    # 红色系
            "computer_black": (60, 60, 60),   # 黑色系
            "export_image": (140, 100, 180),  # 紫色系
            "flip_board": (180, 140, 60),     # 棕色系
            "view_notation": (100, 160, 180), # 青色系
            "first_move": (100, 100, 180),    # 蓝紫色系
            "prev_move": (100, 100, 180),     # 蓝紫色系
            "next_move": (100, 100, 180),     # 蓝紫色系
            "last_move": (100, 100, 180),     # 蓝紫色系
            "edit_position": (220, 140, 60),  # 橙色系
            "stop_thinking": (220, 80, 80),   # 红色系
            "settings": (80, 160, 200)        # 蓝绿色系
        }
        
        # 获取该按钮对应的颜色，默认灰色
        color = icon_colors.get(name, (150, 150, 150))
        
        # 绘制带渐变的背景
        for y in range(icon_size[1]):
            # 创建从上到下的渐变效果
            gradient_factor = y / icon_size[1]
            r = int(color[0] * (1 - gradient_factor * 0.3))
            g = int(color[1] * (1 - gradient_factor * 0.3))
            b = int(color[2] * (1 - gradient_factor * 0.3))
            pygame.draw.line(icon_surface, (r, g, b), (0, y), (icon_size[0], y))
        
        # 映射名称到显示文字
        name_map = {
            "new": "新建",
            "open": "打开",
            "save": "保存",
            "computer_red": "电红",
            "computer_black": "电黑",
            "export_image": "导出",
            "flip_board": "翻转",
            "view_notation": "棋谱",
            "first_move": "开始",
            "prev_move": "上一",
            "next_move": "下一", 
            "last_move": "结束",
            "edit_position": "编辑",
            "stop_thinking": "停止",
            "settings": "设置",
            "separator": ""
        }
        
        # 获取图标文字
        text = name_map.get(name, name)
        
        # 添加图标形状
        if name == "new":
            # 新建 - 绘制一个文档形状
            if supports_border_radius:
                pygame.draw.rect(icon_surface, (255, 255, 255, 200), (5, 5, icon_size[0]-10, icon_size[1]-10), 0, 2)
                pygame.draw.rect(icon_surface, (0, 0, 0, 150), (5, 5, icon_size[0]-10, icon_size[1]-10), 1, 2)
            else:
                pygame.draw.rect(icon_surface, (255, 255, 255, 200), (5, 5, icon_size[0]-10, icon_size[1]-10), 0)
                pygame.draw.rect(icon_surface, (0, 0, 0, 150), (5, 5, icon_size[0]-10, icon_size[1]-10), 1)
            # 添加+号
            pygame.draw.line(icon_surface, (0, 0, 0, 150), (10, icon_size[1]//2), (icon_size[0]-10, icon_size[1]//2), 2)
            pygame.draw.line(icon_surface, (0, 0, 0, 150), (icon_size[0]//2, 10), (icon_size[0]//2, icon_size[1]-10), 2)
        elif name in ["open", "save"]:
            # 打开/保存 - 绘制一个文件夹图标
            if supports_border_radius:
                pygame.draw.rect(icon_surface, (255, 255, 255, 200), (5, 8, icon_size[0]-10, icon_size[1]-16), 0, 2)
                pygame.draw.rect(icon_surface, (0, 0, 0, 150), (5, 8, icon_size[0]-10, icon_size[1]-16), 1, 2)
                # 文件夹顶部
                pygame.draw.rect(icon_surface, (255, 255, 255, 200), (8, 5, icon_size[0]-16, 6), 0, 1)
                pygame.draw.rect(icon_surface, (0, 0, 0, 150), (8, 5, icon_size[0]-16, 6), 1, 1)
            else:
                pygame.draw.rect(icon_surface, (255, 255, 255, 200), (5, 8, icon_size[0]-10, icon_size[1]-16), 0)
                pygame.draw.rect(icon_surface, (0, 0, 0, 150), (5, 8, icon_size[0]-10, icon_size[1]-16), 1)
                # 文件夹顶部
                pygame.draw.rect(icon_surface, (255, 255, 255, 200), (8, 5, icon_size[0]-16, 6), 0)
                pygame.draw.rect(icon_surface, (0, 0, 0, 150), (8, 5, icon_size[0]-16, 6), 1)
        elif name in ["computer_red", "computer_black"]:
            # 电脑方 - 绘制一个电脑/芯片图标
            color = (220, 70, 70, 200) if name == "computer_red" else (50, 50, 50, 200)
            # 电脑显示器
            if supports_border_radius:
                pygame.draw.rect(icon_surface, color, (6, 6, icon_size[0]-12, icon_size[1]-16), 0, 2)
                # 底座
                pygame.draw.rect(icon_surface, color, (icon_size[0]//2-5, icon_size[1]-12, 10, 6), 0, 1)
                pygame.draw.rect(icon_surface, (255, 255, 255, 150), (10, 10, icon_size[0]-20, icon_size[1]-24), 1, 1)
            else:
                pygame.draw.rect(icon_surface, color, (6, 6, icon_size[0]-12, icon_size[1]-16), 0)
                # 底座
                pygame.draw.rect(icon_surface, color, (icon_size[0]//2-5, icon_size[1]-12, 10, 6), 0)
                pygame.draw.rect(icon_surface, (255, 255, 255, 150), (10, 10, icon_size[0]-20, icon_size[1]-24), 1)
        elif name in ["first_move", "prev_move", "next_move", "last_move"]:
            # 移动按钮 - 绘制箭头
            arrow_color = (255, 255, 255, 220)
            if name == "first_move":
                # 开始：双左箭头
                points1 = [(icon_size[0]//2+3, 5), (icon_size[0]//2+3, icon_size[1]-5), (5, icon_size[1]//2)]
                points2 = [(icon_size[0]-5, 5), (icon_size[0]-5, icon_size[1]-5), (icon_size[0]//2+8, icon_size[1]//2)]
                pygame.draw.polygon(icon_surface, arrow_color, points1)
                pygame.draw.polygon(icon_surface, arrow_color, points2)
            elif name == "prev_move":
                # 上一步：左箭头
                points = [(icon_size[0]-5, 5), (icon_size[0]-5, icon_size[1]-5), (5, icon_size[1]//2)]
                pygame.draw.polygon(icon_surface, arrow_color, points)
            elif name == "next_move":
                # 下一步：右箭头
                points = [(5, 5), (5, icon_size[1]-5), (icon_size[0]-5, icon_size[1]//2)]
                pygame.draw.polygon(icon_surface, arrow_color, points)
            elif name == "last_move":
                # 结束：双右箭头
                points1 = [(5, 5), (5, icon_size[1]-5), (icon_size[0]//2-3, icon_size[1]//2)]
                points2 = [(icon_size[0]//2+2, 5), (icon_size[0]//2+2, icon_size[1]-5), (icon_size[0]-5, icon_size[1]//2)]
                pygame.draw.polygon(icon_surface, arrow_color, points1)
                pygame.draw.polygon(icon_surface, arrow_color, points2)
        
        # 绘制按钮边框
        if supports_border_radius:
            pygame.draw.rect(icon_surface, (0, 0, 0, 100), (0, 0, icon_size[0], icon_size[1]), 1, 3)
        else:
            pygame.draw.rect(icon_surface, (0, 0, 0, 100), (0, 0, icon_size[0], icon_size[1]), 1)
        
        # 在小图标不够直观的情况下，添加文字标签
        if name not in ["first_move", "prev_move", "next_move", "last_move", "new"]:
            # 渲染文字
            font = pygame.font.Font(None, 14)
            text_surface = font.render(text, True, (255, 255, 255))
            text_rect = text_surface.get_rect(center=(icon_surface.get_width() // 2, icon_surface.get_height() // 2))
            icon_surface.blit(text_surface, text_rect)
        
        return icon_surface
    
    def create_buttons(self):
        """创建所有工具栏按钮"""
        # 添加按钮的函数
        def add_button(icon_name, tooltip, action=None):
            button = ToolbarButton(
                icon=self.icons.get(icon_name),
                x=self.current_x,
                y=2,
                width=self.button_width,
                height=self.button_height,
                tooltip=tooltip,
                action=action
            )
            self.buttons.append(button)
            self.current_x += self.button_width + 2
        
        # 添加分隔符
        def add_separator():
            self.current_x += self.separator_width
        
        # 新建、打开、保存
        add_button("new", "新建棋局", lambda: self.game.reset_game())
        add_button("open", "打开棋局", lambda: self.game.open_pgn_dialog())
        add_button("save", "保存棋局", lambda: self.game.save_pgn_dialog())
        add_separator()
        
        # 电脑执红/黑
        add_button("computer_red", "电脑执红", lambda: self.game.set_computer_side(0))
        add_button("computer_black", "电脑执黑", lambda: self.game.set_computer_side(1))
        add_separator()
        
        # 生成图片、翻转棋盘、查看棋谱
        add_button("export_image", "生成图片棋盘", lambda: self.game.export_board_image())
        add_button("flip_board", "翻转棋盘", lambda: self.game.flip_board())
        add_button("view_notation", "查看文本棋谱", lambda: self.game.view_text_notation())
        add_separator()
        
        # 棋谱浏览
        add_button("first_move", "起始局面", lambda: self.game.move_to_start())
        add_button("prev_move", "上一着", lambda: self.game.move_history_backward())
        add_button("next_move", "下一着", lambda: self.game.move_history_forward())
        add_button("last_move", "最后局面", lambda: self.game.move_to_end())
        add_separator()
        
        # 编辑局面、停止思考、设置级别
        add_button("edit_position", "编辑局面", lambda: self.game.edit_position())
        add_button("stop_thinking", "停止思考立即走棋", lambda: self.game.stop_ai_thinking())
        add_button("settings", "设置级别", lambda: self.game.show_settings())
    
    def handle_event(self, event):
        """处理工具栏事件"""
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            mouse_pos = pygame.mouse.get_pos()
            
            # 检查是否点击了工具栏上的按钮
            for button in self.buttons:
                if button.is_clicked(mouse_pos):
                    if button.action:
                        button.action()
                    return True
        
        # 鼠标移动事件，用于显示工具提示
        elif event.type == pygame.MOUSEMOTION:
            mouse_pos = pygame.mouse.get_pos()
            found_hover = False
            
            # 检查鼠标是否悬停在按钮上
            if mouse_pos[1] < self.height:  # 只在工具栏区域内检测
                for button in self.buttons:
                    if button.is_hovered(mouse_pos):
                        found_hover = True
                        # 如果之前没有显示tooltip或者是不同的按钮
                        if not self.tooltip_visible or self.current_tooltip != button.tooltip:
                            self.current_tooltip = button.tooltip
                            self.tooltip_pos = mouse_pos
                            self.tooltip_time = pygame.time.get_ticks()
                            self.tooltip_visible = False  # 重置计时器
                        break
            
            # 如果没有悬停在任何按钮上，隐藏tooltip
            if not found_hover:
                self.tooltip_visible = False
                self.current_tooltip = None
        
        return False
    
    def render(self, screen):
        """渲染工具栏"""
        # 绘制工具栏背景（渐变效果）
        # 创建渐变背景
        for y in range(self.height):
            progress = y / self.height
            r = int(self.toolbar_gradient_top[0] * (1 - progress) + self.toolbar_gradient_bottom[0] * progress)
            g = int(self.toolbar_gradient_top[1] * (1 - progress) + self.toolbar_gradient_bottom[1] * progress)
            b = int(self.toolbar_gradient_top[2] * (1 - progress) + self.toolbar_gradient_bottom[2] * progress)
            pygame.draw.line(screen, (r, g, b), (0, y), (screen.get_width(), y))
        
        # 绘制底部边框
        pygame.draw.line(screen, self.toolbar_border, (0, self.height), (screen.get_width(), self.height), 1)
        
        # 渲染所有按钮
        for button in self.buttons:
            button.render(screen)
        
        # 渲染工具提示（如果有）
        current_time = pygame.time.get_ticks()
        if self.current_tooltip and not self.tooltip_visible:
            # 检查是否达到显示延迟
            if current_time - self.tooltip_time > self.tooltip_delay * 1000:
                self.tooltip_visible = True
        
        if self.current_tooltip and self.tooltip_visible:
            self.render_tooltip(screen, self.current_tooltip, self.tooltip_pos)
    
    def render_tooltip(self, screen, tooltip_text, position):
        """渲染工具提示"""
        # 确保有字体
        if not hasattr(self, 'toolbar_font') or not self.toolbar_font:
            try:
                from pygame import freetype
                if freetype.get_init():
                    self.toolbar_font = freetype.SysFont(None, 16)  # 增大字体尺寸
                else:
                    self.toolbar_font = pygame.font.Font(None, 16)  # 增大字体尺寸
            except ImportError as e:
                # 如果无法导入freetype，则使用普通font
                print(f"无法导入freetype模块: {e}")
                self.toolbar_font = pygame.font.Font(None, 16)  # 增大字体尺寸
            except Exception as e:
                # 其他异常情况
                print(f"设置字体时出错: {e}")
                self.toolbar_font = pygame.font.Font(None, 16)  # 增大字体尺寸
        
        # 渲染工具提示文本
        tooltip_surface = self.toolbar_font.render(tooltip_text, True, (0, 0, 0))
        tooltip_width = tooltip_surface.get_width() + 16  # 增加宽度
        tooltip_height = tooltip_surface.get_height() + 10  # 增加高度
        
        # 确保工具提示不会超出屏幕边界
        x = position[0]
        if x + tooltip_width > screen.get_width():
            x = screen.get_width() - tooltip_width
        
        # 绘制工具提示背景 - 使用渐变效果
        tooltip_rect = pygame.Rect(x, self.height + 2, tooltip_width, tooltip_height)
        
        # 创建渐变背景
        tooltip_surface_bg = pygame.Surface((tooltip_width, tooltip_height), pygame.SRCALPHA)
        for y in range(tooltip_height):
            progress = y / tooltip_height
            r = int(255 * (1 - progress * 0.1))
            g = int(255 * (1 - progress * 0.1))
            b = int(220 * (1 - progress * 0.1))
            pygame.draw.line(tooltip_surface_bg, (r, g, b), (0, y), (tooltip_width, y))
        
        # 创建圆角的tooltip（如果pygame版本支持）
        if supports_border_radius:
            # 创建带圆角的矩形
            pygame.draw.rect(tooltip_surface_bg, (0, 0, 0, 25), pygame.Rect(0, 0, tooltip_width, tooltip_height), 0, 5)
            screen.blit(tooltip_surface_bg, (x, self.height + 2))
            pygame.draw.rect(screen, (100, 100, 100), tooltip_rect, 1, 5)  # 圆角边框
        else:
            # 不支持圆角的版本
            screen.blit(tooltip_surface_bg, (x, self.height + 2))
            pygame.draw.rect(screen, (100, 100, 100), tooltip_rect, 1)
        
        # 添加小三角形指示器
        triangle_points = [
            (position[0], self.height + 2),
            (position[0] - 5, self.height - 4),
            (position[0] + 5, self.height - 4)
        ]
        pygame.draw.polygon(screen, (255, 255, 220), triangle_points)
        pygame.draw.polygon(screen, (100, 100, 100), triangle_points, 1)
        
        # 绘制工具提示文本 - 添加轻微阴影效果
        screen.blit(tooltip_surface, (x + 8, self.height + 5))


class ToolbarButton:
    def __init__(self, icon, x, y, width, height, tooltip="", action=None):
        self.icon = icon
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.tooltip = tooltip
        self.action = action
        
        # 按钮状态颜色
        self.normal_color = (220, 220, 220)
        self.hover_color = (200, 200, 200)
        self.active_color = (180, 180, 180)
        self.border_color = (150, 150, 150)
        self.hover_border_color = (100, 100, 100)
        self.current_color = self.normal_color
        self.current_border_color = self.border_color
        
        # 按钮状态
        self.hovered = False
        self.active = False
        
        # 添加按钮动画效果
        self.pressed = False
        self.press_offset = 0
        self.animation_speed = 0.2  # 动画速度
        self.press_time = 0
    
    def is_clicked(self, pos):
        """检查按钮是否被点击"""
        x, y = pos
        if self.x <= x <= self.x + self.width and self.y <= y <= self.y + self.height:
            self.pressed = True
            self.press_time = pygame.time.get_ticks()
            return True
        return False
    
    def is_hovered(self, pos):
        """检查鼠标是否悬停在按钮上"""
        x, y = pos
        was_hovered = self.hovered
        self.hovered = self.x <= x <= self.x + self.width and self.y <= y <= self.y + self.height
        
        # 如果悬停状态发生变化，更新颜色
        if self.hovered != was_hovered:
            if self.hovered:
                self.current_color = self.hover_color
                self.current_border_color = self.hover_border_color
            else:
                self.current_color = self.normal_color
                self.current_border_color = self.border_color
        
        return self.hovered
    
    def render(self, screen):
        """渲染按钮"""
        # 更新按钮动画效果
        current_time = pygame.time.get_ticks()
        
        if self.pressed:
            elapsed = (current_time - self.press_time) / 1000.0
            if elapsed < self.animation_speed:
                # 按下动画：按钮向下移动
                self.press_offset = int(2 * (1 - elapsed / self.animation_speed))
            else:
                self.pressed = False
                self.press_offset = 0
        
        # 绘制按钮背景
        button_rect = pygame.Rect(
            self.x, 
            self.y + self.press_offset, 
            self.width, 
            self.height
        )
        
        # 渐变背景效果
        if supports_border_radius:
            pygame.draw.rect(screen, self.current_color, button_rect, 0, 3)
            # 绘制按钮边框
            pygame.draw.rect(screen, self.current_border_color, button_rect, 1, 3)
        else:
            pygame.draw.rect(screen, self.current_color, button_rect, 0)
            # 绘制按钮边框
            pygame.draw.rect(screen, self.current_border_color, button_rect, 1)
        
        # 如果有图标，绘制图标
        if self.icon:
            # 计算图标位置（居中）
            icon_x = self.x + (self.width - self.icon.get_width()) // 2
            icon_y = self.y + (self.height - self.icon.get_height()) // 2 + self.press_offset
            screen.blit(self.icon, (icon_x, icon_y))
