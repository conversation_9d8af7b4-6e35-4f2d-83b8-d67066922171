import os
from PIL import Image


def fix_png_images(directory):
    """修复PNG图片的ICC配置文件问题"""
    for filename in os.listdir(directory):
        if filename.lower().endswith('.png'):
            filepath = os.path.join(directory, filename)
            try:
                # 打开图片并移除ICC配置文件
                img = Image.open(filepath)
                if 'icc_profile' in img.info:
                    # 创建一个没有ICC配置文件的副本
                    data = list(img.getdata())
                    new_img = Image.new(img.mode, img.size)
                    new_img.putdata(data)

                    # 保存优化后的图片
                    new_img.save(filepath, optimize=True)
                    print(f"已修复: {filename}")
                else:
                    print(f"无需修复: {filename}")
            except Exception as e:
                print(f"处理 {filename} 时出错: {e}")


if __name__ == "__main__":
    # 修复assets/images/目录下的所有PNG图片
    image_dir = os.path.join('assets', 'images')
    if os.path.exists(image_dir):
        fix_png_images(image_dir)
    else:
        print(f"目录不存在: {image_dir}")
