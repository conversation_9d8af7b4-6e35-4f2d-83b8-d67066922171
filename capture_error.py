import sys
import os
import traceback

# 启用未处理异常的详细跟踪
sys.tracebacklimit = None

try:
    # 重定向标准输出和错误输出到文件
    original_stdout = sys.stdout
    original_stderr = sys.stderr
    
    error_file = open('error_output.txt', 'w', encoding='utf-8')
    sys.stdout = error_file
    sys.stderr = error_file
    
    print("=== 执行程序开始 ===")
    
    # 尝试导入和运行主模块
    print("导入main模块...")
    import importlib.util
    spec = importlib.util.spec_from_file_location("main", "main.py")
    main = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(main)
    
    print("=== 执行程序结束 ===")
    
except Exception as e:
    print(f"\n出现异常: {type(e).__name__}: {e}")
    print("\n完整的异常堆栈跟踪:")
    traceback.print_exc()
    
finally:
    # 恢复标准输出和错误输出
    if 'error_file' in locals():
        sys.stdout = original_stdout
        sys.stderr = original_stderr
        error_file.close()
        print(f"程序执行完毕，错误信息已保存到 {os.path.abspath('error_output.txt')}")
