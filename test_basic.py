import sys
import traceback

print("基本测试开始")

try:
    print("测试 Python 解释器...")
    print(f"Python 版本: {sys.version}")
    
    print("\n测试 pygame 导入...")
    import pygame
    print(f"Pygame 版本: {pygame.version.ver}")
    
    print("\n测试基本常量...")
    from constants import WINDOW_WIDTH, WINDOW_HEIGHT, TOOLBAR_HEIGHT, TITLE
    print(f"窗口尺寸: {WINDOW_WIDTH}x{WINDOW_HEIGHT}")
    print(f"工具栏高度: {TOOLBAR_HEIGHT}")
    
    print("\n测试 pygame 初始化...")
    pygame.init()
    print("pygame 初始化成功")
    
    print("\n测试窗口创建...")
    screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT + TOOLBAR_HEIGHT))
    pygame.display.set_caption(TITLE)
    print("窗口创建成功")
    
    pygame.quit()
    print("\n基本测试通过")
    
except Exception as e:
    print(f"\n错误: {e}")
    traceback.print_exc()
