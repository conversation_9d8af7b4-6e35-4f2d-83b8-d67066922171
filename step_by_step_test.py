import sys
import traceback

def test_import(module_name):
    try:
        print(f"尝试导入模块: {module_name}")
        __import__(module_name)
        print(f"成功导入模块: {module_name}")
        return True
    except Exception as e:
        print(f"导入模块 {module_name} 失败: {type(e).__name__}: {e}")
        traceback.print_exc()
        return False

# 测试所有相关模块
modules = [
    "pygame",
    "constants",
    "board",
    "piece",
    "button",
    "toolbar",
    "sound_manager",
    "game",
    "main"
]

# 逐个导入模块
for module in modules:
    if not test_import(module):
        print(f"\n模块 {module} 导入失败，停止测试")
        break
    print("-" * 50)

print("\n全部测试完成")
