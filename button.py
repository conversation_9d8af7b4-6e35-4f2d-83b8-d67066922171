import pygame

class Button:
    def __init__(self, text, x, y, width, height, action=None):
        self.text = text
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.action = action
        self.font = pygame.font.Font(None, 32)
        
        # 按钮颜色
        self.normal_color = (200, 200, 200)
        self.hover_color = (150, 150, 150)
        self.current_color = self.normal_color
    
    def is_clicked(self, pos):
        x, y = pos
        if self.x <= x <= self.x + self.width and self.y <= y <= self.y + self.height:
            return True
        return False
    
    def update(self, mouse_pos):
        x, y = mouse_pos
        if self.x <= x <= self.x + self.width and self.y <= y <= self.y + self.height:
            self.current_color = self.hover_color
        else:
            self.current_color = self.normal_color
    
    def render(self, screen):
        # 更新按钮状态
        self.update(pygame.mouse.get_pos())
        
        # 绘制按钮背景
        pygame.draw.rect(screen, self.current_color, (self.x, self.y, self.width, self.height))
        pygame.draw.rect(screen, (0, 0, 0), (self.x, self.y, self.width, self.height), 2)
        
        # 绘制按钮文本
        text_surface = self.font.render(self.text, True, (0, 0, 0))
        text_rect = text_surface.get_rect(center=(self.x + self.width // 2, self.y + self.height // 2))
        screen.blit(text_surface, text_rect)