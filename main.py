# from constants import WINDOW_WIDTH, WINDOW_HEIGHT, TITLE, TOOLBAR_HEIGHT
# from game import Game
# import traceback
print("Starting main.py...")
# import pygame
# import sys
# import os


def ensure_font_directory():
    """确保fonts目录存在，并下载中文字体（如果需要）"""
    font_dir = os.path.join(os.path.dirname(
        os.path.abspath(__file__)), "fonts")

    # 创建fonts目录（如果不存在）
    if not os.path.exists(font_dir):
        os.makedirs(font_dir)
        print(f"创建字体目录: {font_dir}")

    # 检查字体文件是否存在
    font_path = os.path.join(font_dir, "simhei.ttf")
    if not os.path.exists(font_path):
        print("未找到中文字体文件，请手动下载黑体字体(simhei.ttf)并放入fonts目录")
        print(f"字体目录路径: {font_dir}")
        print("您可以从Windows系统的C:\\Windows\\Fonts目录复制simhei.ttf文件")
        print("或者从网络下载开源中文字体")
    else:
        print(f"已找到中文字体: {font_path}")

    return font_dir


def ensure_assets_directories():
    """确保所有资源目录存在"""
    base_dir = os.path.dirname(os.path.abspath(__file__))

    # 需要确保存在的目录
    asset_dirs = [
        os.path.join(base_dir, "assets"),
        os.path.join(base_dir, "assets", "sounds"),
        os.path.join(base_dir, "assets", "icons")
    ]

    # 创建目录
    for asset_dir in asset_dirs:
        if not os.path.exists(asset_dir):
            os.makedirs(asset_dir)
            print(f"创建资源目录: {asset_dir}")


def setup_logging():
    """设置日志记录"""
    # 创建日志目录
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 创建日志文件
    log_file = os.path.join(log_dir, "game_log.txt")

    # 返回日志文件路径
    return log_file


def main():
    # 添加更多详细的日志
    print("================================")
    print("游戏启动中...详细日志:")
    print("================================")

    print("步骤 1: 尝试执行 ensure_font_directory()")
    try:
        # 确保字体目录存在
        print("检查字体目录...")
        ensure_font_directory()
        print("步骤 1: ensure_font_directory() 执行成功")
    except Exception as e:
        print(f"步骤 1: ensure_font_directory() 执行失败: {e}")
        traceback.print_exc()
        sys.exit(1)

    print("步骤 2: 尝试执行 ensure_assets_directories()")
    try:
        # 确保资源目录存在
        print("检查资源目录...")
        ensure_assets_directories()
        print("步骤 2: ensure_assets_directories() 执行成功")
    except Exception as e:
        print(f"步骤 2: ensure_assets_directories() 执行失败: {e}")
        traceback.print_exc()
        sys.exit(1)

    print("步骤 3: 尝试初始化 Pygame")
    try:
        # 初始化pygame
        print("初始化pygame...")
        pygame.init()
        print("初始化pygame.mixer...")
        pygame.mixer.init()
        print("步骤 3: Pygame 初始化成功")
    except Exception as e:
        print(f"步骤 3: Pygame 初始化失败: {e}")
        traceback.print_exc()
        sys.exit(1)

    print("步骤 4: 尝试创建游戏窗口")
    try:
        # 创建游戏窗口
        print("创建游戏窗口...")
        screen = pygame.display.set_mode(
            (WINDOW_WIDTH, WINDOW_HEIGHT + TOOLBAR_HEIGHT))
        pygame.display.set_caption(TITLE)
        print("步骤 4: 游戏窗口创建成功")
    except Exception as e:
        print(f"步骤 4: 游戏窗口创建失败: {e}")
        traceback.print_exc()
        sys.exit(1)

    print("步骤 5: 尝试创建游戏实例")
    try:
        print("创建游戏实例...")
        game = Game(screen)
        print("步骤 5: 游戏实例创建成功")

        # 测试sound_manager功能
        print("测试sound_manager...")
        if hasattr(game, 'sound_manager'):
            print(f"sound_manager属性: {dir(game.sound_manager)}")
            if hasattr(game.sound_manager, 'play'):
                print("sound_manager.play方法存在")
            if hasattr(game.sound_manager, 'play_sound'):
                print("sound_manager.play_sound方法存在")
            print(
                f"已加载音效: {list(game.sound_manager.sounds.keys()) if hasattr(game.sound_manager, 'sounds') else '无'}")
        else:
            print("警告: game对象没有sound_manager属性")

        print("步骤 6: 尝试启动游戏主循环")
        try:
            print("启动游戏...")
            game.run()
            print("步骤 6: 游戏主循环结束")
        except Exception as e:
            # 详细记录游戏运行时错误
            error_msg = f"游戏运行出错: {type(e).__name__}: {e}"
            traceback_msg = traceback.format_exc()
            print(error_msg)
            print("详细错误信息:")
            print(traceback_msg)

            # 将错误信息写入日志文件
            with open("error_log.txt", "w", encoding="utf-8") as f:
                f.write(f"{error_msg}\n")
                f.write(traceback_msg)
            sys.exit(1)  # 确保在运行时错误时退出
    except Exception as e:
        # 记录游戏创建时的错误
        error_msg = f"游戏创建失败: {type(e).__name__}: {e}"
        traceback_msg = traceback.format_exc()
        print(error_msg)
        print("详细错误信息:")
        print(traceback_msg)

        # 将错误信息写入日志文件
        with open("error_log.txt", "w", encoding="utf-8") as f:
            f.write(f"{error_msg}\n")
            f.write(traceback_msg)
        sys.exit(1)  # 确保在创建错误时退出

    print("步骤 7: 退出 Pygame")
    # 退出pygame
    pygame.quit()
    sys.exit()


if __name__ == "__main__":
    main()
