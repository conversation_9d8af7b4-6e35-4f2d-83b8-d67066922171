import sys
import importlib
import traceback

# 强制重新加载模块
if 'game' in sys.modules:
    print("重新加载game模块...")
    del sys.modules['game']

try:
    # 导入game模块
    print("导入game模块...")
    import game
    
    # 检查模块属性
    print("\nGame类方法:")
    game_methods = [method for method in dir(game.Game) if not method.startswith('__')]
    for method in sorted(game_methods):
        print(f"- {method}")
    
    # 检查是否存在check_game_over方法
    if 'check_game_over' in game_methods:
        print("\n✓ check_game_over方法存在")
    else:
        print("\n✗ check_game_over方法不存在!")
        
    # 创建Game实例
    print("\n尝试创建Game实例...")
    import pygame
    pygame.init()
    from constants import WINDOW_WIDTH, WINDOW_HEIGHT, TOOLBAR_HEIGHT
    screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT + TOOLBAR_HEIGHT))
    g = game.Game(screen)
    
    # 验证方法
    print("检查Game实例的方法...")
    if hasattr(g, 'check_game_over'):
        print("✓ Game实例有check_game_over方法")
    else:
        print("✗ Game实例没有check_game_over方法!")
    
    pygame.quit()
    
except Exception as e:
    print(f"错误: {type(e).__name__}: {e}")
    traceback.print_exc()
