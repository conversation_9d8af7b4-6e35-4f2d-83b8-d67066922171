import pygame
import os
import sys
from game import Game
from constants import WINDOW_WIDTH, WINDOW_HEIGHT, TOOLBAR_HEIGHT, TITLE

def create_simple_sound_files():
    """创建简单的音效文件，用于测试"""
    sound_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "assets", "sounds")
    if not os.path.exists(sound_dir):
        os.makedirs(sound_dir)
    
    # 创建空白的WAV文件
    sound_files = ["move.wav", "capture.wav", "check.wav", "button.wav", "select.wav", "illegal.wav"]
    
    # 确保目录存在
    if not os.path.exists(sound_dir):
        os.makedirs(sound_dir)
    
    for filename in sound_files:
        filepath = os.path.join(sound_dir, filename)
        if not os.path.exists(filepath):
            print(f"创建空白音效文件: {filepath}")
            with open(filepath, 'wb') as f:
                # 创建一个简单的WAV文件头
                f.write(b'RIFF')
                f.write((36).to_bytes(4, byteorder='little'))  # 文件大小-8
                f.write(b'WAVE')
                f.write(b'fmt ')
                f.write((16).to_bytes(4, byteorder='little'))  # fmt块大小
                f.write((1).to_bytes(2, byteorder='little'))   # 音频格式
                f.write((1).to_bytes(2, byteorder='little'))   # 通道数
                f.write((22050).to_bytes(4, byteorder='little'))  # 采样率
                f.write((22050).to_bytes(4, byteorder='little'))  # 字节率
                f.write((1).to_bytes(2, byteorder='little'))   # 块对齐
                f.write((8).to_bytes(2, byteorder='little'))   # 位宽
                f.write(b'data')
                f.write((0).to_bytes(4, byteorder='little'))   # 数据大小

def main():
    """初始化并测试集成功能"""
    # 创建必要的声音文件
    create_simple_sound_files()
    
    # 初始化pygame
    pygame.init()
    pygame.display.set_caption(TITLE)
    
    # 创建游戏窗口
    screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT + TOOLBAR_HEIGHT))
    
    # 创建游戏实例
    game = Game(screen)
    
    # 切换到游戏状态而不是菜单
    game.start_game(vs_ai=True)
    
    # 启动游戏
    game.run()
    
    # 退出pygame
    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()
