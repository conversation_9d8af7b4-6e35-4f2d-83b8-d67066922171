import pygame
import os

class SoundManager:
    def __init__(self):
        # 初始化pygame混音器
        pygame.mixer.init()
        
        # 音效开关
        self.sound_enabled = True
        
        # 加载音效
        self.sounds = {}
        self.load_sounds()
    
    def load_sounds(self):
        """加载所有音效文件"""
        # 音效文件目录
        sound_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "assets", "sounds")
        
        # 确保音效目录存在
        if not os.path.exists(sound_dir):
            os.makedirs(sound_dir)
            print(f"创建音效目录: {sound_dir}")
        
        # 音效文件名和对应的键
        sound_files = {
            "select": "select.wav",    # 点选棋子
            "move": "move.wav",        # 走棋
            "capture": "capture.wav",  # 吃子
            "check": "check.wav",      # 将军
            "checkmate": "checkmate.wav",  # 将死
            "button": "button.wav",    # 按钮点击
            "illegal": "illegal.wav",  # 非法走法
            "start": "start.wav",      # 开始游戏
            "win": "win.wav",          # 胜利
            "lose": "lose.wav"         # 失败
        }
        
        # 加载音效文件
        for key, filename in sound_files.items():
            sound_path = os.path.join(sound_dir, filename)
            
            # 检查文件是否存在，如果不存在，尝试查找不区分大小写的相同文件名
            if not os.path.exists(sound_path):
                # 获取目录中所有文件
                try:
                    all_files = os.listdir(sound_dir)
                    # 查找相同文件名但可能大小写不同的文件
                    for file in all_files:
                        if file.lower() == filename.lower():
                            sound_path = os.path.join(sound_dir, file)
                            break
                except Exception as e:
                    print(f"读取音效目录失败: {e}")
            
            if os.path.exists(sound_path):
                try:
                    self.sounds[key] = pygame.mixer.Sound(sound_path)
                    print(f"加载音效: {sound_path}")
                except Exception as e:
                    print(f"加载音效失败 {sound_path}: {e}")
            else:
                print(f"音效文件不存在: {filename}")
    
    def play(self, sound_name):
        """播放指定音效"""
        if not self.sound_enabled:
            return
            
        if sound_name in self.sounds:
            self.sounds[sound_name].play()
        else:
            print(f"未找到音效: {sound_name}")
    
    def play_sound(self, sound_name):
        """播放指定音效（play方法的别名，保持向后兼容）"""
        return self.play(sound_name)
    
    def toggle_sound(self):
        """切换音效开关"""
        self.sound_enabled = not self.sound_enabled
        return self.sound_enabled
    
    def set_sound_enabled(self, enabled):
        """设置音效开关"""
        self.sound_enabled = enabled
